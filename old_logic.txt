
// int can_move_piece(Piece *p, int die_roll, int* target_pos, int* is_capture, int* path_blocked) {
//     *path_blocked = 0;
    
//     // Safety check
//     if (!p) {
//         printf("ERROR: Null piece pointer\n");
//         return 0;
//     }
    
//     if (p->is_in_base) {
//         if (die_roll != 6) return 0; // can't come out

//         int start = START_POSITION[p->color];
//         Cell* cell = &board[start];

//         if (cell->count == 0) {
//             if (target_pos) *target_pos = start;
//             if (is_capture) *is_capture = 0;
//             return 1;
//         }
        
//         // capture if opponent
//         if ((cell->count == 1) && (cell->occupants[0]->color != p->color)) {
//             if (target_pos) *target_pos = start;
//             if (is_capture) *is_capture = 1;
//             return 1;
//         }
        
//         // Can stack with same color pieces
//         if (cell->count >= 1 && cell->occupants[0]->color == p->color) {
//             if (target_pos) *target_pos = start;
//             if (is_capture) *is_capture = 0;
//             return 1;
//         }

//         return 0; // blocked by opponent
//     }

//     if (p->is_in_board) {
//         // Safety check for board position
//         if (p->board_pos < 0 || p->board_pos >= STD_BOARD_SIZE) {
//             printf("ERROR: Invalid board position %d for piece G%d\n", p->board_pos, p->id);
//             return 0;
//         }
//         // get dest cell
//         int new_pos = (p->board_pos + die_roll) % STD_BOARD_SIZE;
//         Cell* dest_cell = &board[new_pos];


//         // Check if destination is blocked by opponents
//         if (dest_cell->count >= 2 && dest_cell->occupants[0]->color != p->color) {
//             if (is_capture) *is_capture = 0;
//             return 0; // blocked 
//         }

//         // check for home entry and blocks
//         int home_entry = HOME_ENTRY_POSITION[p->color];
//         printf("DEBUG: Piece %s%d at pos %d, die_roll %d, dist_from_home is %d, dist_from_start is %d, home_entry at %d\n", get_color_name(p->color),
//                p->id, p->board_pos, die_roll,p->remaining_to_home-die_roll, p->dist_from_start+die_roll, home_entry);
        
//         // Check if the move would cross or land on the home entry position
//         int crosses_home_entry = 0;
//         int steps_into_home = 0;
        
//         for (int i = 1; i <= die_roll; i++) {
//             int check_pos = (p->board_pos + i) % STD_BOARD_SIZE;
//             Cell* check_cell = &board[check_pos];
            
//             // If there's a block of 2+ opponent pieces in the path
//             if (check_cell->count >= 2 && check_cell->occupants[0]->color != p->color) {
//                 *path_blocked = 1;
//                 if (check_pos-1 <= 0) {
//                     Cell *check_previous = &board[check_pos-1];
//                     if (check_previous->count >= 2 && check_previous->occupants[0]->color != p->color) return 0;
//                     if (target_pos) *target_pos = check_pos-1;
//                     if (is_capture && check_previous->count>0 && check_previous->occupants[0]->color!=p->color){ // capture if opponent
//                         *is_capture = 1;
//                     } else if (is_capture) {
//                         *is_capture = 0;
//                     } 
//                     return 1;
//                 } else {
//                     return 0;
//                 }
//             }

//             if (check_pos == home_entry && (p->remaining_to_home)-i <= 6) {
//                 crosses_home_entry = 1;
//                 steps_into_home = die_roll - i; // Steps remaining after reaching home entry
//                 printf("DEBUG: Crosses home entry at step %d, steps_into_home = %d\n", i, steps_into_home);
//                 break;
//             }
//         }
        
//         // If crossing home entry, check if we can enter home path
//         if (crosses_home_entry && steps_into_home>0) {
//             // steps_into_home is the number of steps after reaching home entry position
//             if (steps_into_home >= HOME_PATH) {
//                 printf("DEBUG: Would overshoot home path (steps_into_home=%d, HOME_PATH=%d)\n", 
//                        steps_into_home, HOME_PATH);
//                 return 0; // Overshoot home path
//             }
            
//             printf("DEBUG: Entering home path at index %d\n", steps_into_home);
//             if (target_pos) *target_pos = steps_into_home-1; // Home index starts from 0
//             if (is_capture) *is_capture = 0;
//             return 1;
//         }
    


//         // Can capture single opponent piece
//         if (dest_cell->count == 1 && dest_cell->occupants[0]->color != p->color) {
//             if (target_pos) *target_pos = new_pos;
//             if (is_capture) *is_capture = 1;
//             return 1;
//         }

//         // Can move to empty space or stack with same color
//         if (dest_cell->count == 0 || 
//             (dest_cell->count >= 1 && dest_cell->occupants[0]->color == p->color)) {
//             if (target_pos) *target_pos = new_pos;
//             if (is_capture) *is_capture = 0;
//             return 1;
//         }

//         return 0;
//     }

//     if (p->is_in_home_path) {
//         // Safety check for home index
//         if (p->home_index < 0 || p->home_index >= HOME_PATH) {
//             printf("ERROR: Invalid home index %d for piece %s%d\n", p->home_index, get_color_name(p->color),p->id);
//             return 0;
//         }
        
//         // if (p->home_index + die_roll > HOME_PATH - 1) {
//         //     printf("DEBUG: Would overshoot final home (current=%d, die_roll=%d, HOME_PATH=%d)\n", 
//         //            p->home_index, die_roll, HOME_PATH);
//         //     return 0; // overshoot
//         // }
        
//         if (p->home_index + die_roll == HOME_PATH - 1) {
//             // reaching home (final position)
//             printf("DEBUG: Reaching final home position\n");
//             if (target_pos) *target_pos = HOME_PATH;
//             if (is_capture) *is_capture = 0;
//             return 1;
//         }
        
//         // // moving within home path
//         // if (target_pos) *target_pos = p->home_index + die_roll;
//         // if (is_capture) *is_capture = 0;
//         // return 1;
//     }
    
//     return 0;
// }


// apply move 
    // movement within home path
  //if (p->is_in_home_path) {
        // Safety check
        // if (target_pos < 0 || target_pos >= HOME_PATH) {
        //     printf("ERROR: Invalid target_pos %d for home path move\n", target_pos);
        //     return;
        // }
        
        // if (target_pos == HOME_PATH) {
        //     // Piece has reached home
        //     printf("DEBUG: Piece %s%d reaches final home\n",get_color_name(p->color), p->id);
        //     p->home_index = -1;
        //     p->is_in_home = 1;
        //     p->remaining_to_home = 0;
        //     p->is_in_home_path = 0;
        //     p -> board_pos = -1;
        //     p -> is_in_board = 0;
        //     p -> is_in_base = 0;
        //     p -> dist_from_start = STD_BOARD_SIZE;
        //     return;
        // }
        // } else {
        //     p->home_index = target_pos;
        //     p->remaining_to_home = HOME_PATH - target_pos - 1;
        // }
//     }
// }


// // Done: red
// int play_red_turn(int die_roll) {
//     Piece* best_piece = NULL;
//     int pieces_on_board = 0;
//     int highest_progress = -1;
//     int best_target = -1;
//     int best_capture = 0;
//     for (int i =0; i <PIECES_PER_PLAYER; i++) {
//         int take_from_base = 0;
//         int to_home = 0;
//         int will_block = 0;
//         Piece *p = &all_pieces[RED][i];
//         int target, is_capture, path_blocked;
//         if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked)) {
//             if (p->is_in_board) pieces_on_board++;
//             int progress = (p-> is_in_board) ? (p->remaining_to_home-die_roll) : (p->is_in_home_path) ? p-> home_index : 0;
        
//             // capture prioritize
//             if (is_capture) {
//                 if (!best_piece || progress > highest_progress) {
//                     best_piece = p;
//                     best_target = target;
//                     best_capture = 1;
//                     highest_progress = progress;
//                 }
//                 continue;
//             }
//             if (p->is_in_home_path) {
//                 best_piece = p;
//                 to_home = 1;
//                 best_capture = 0;
//                 best_target = target;
//                 continue;
//             }
//             // bring in if no capture
//             if (p->is_in_base && die_roll == 6 && pieces_on_board <1 && !is_capture && !to_home) {
//                 Cell *start = &board[START_POSITION[RED]];
//                 //if (start->count == 0 || (start->count == 1 && start->occupants[0]->color == RED)) will_block=1;
//                 best_piece = p;
//                 best_target = START_POSITION[RED];
//                 take_from_base=1;
//                 best_capture=0;
//                 continue;
//             }

//             // avoid blocks 
//             Cell *dest = &board[target];
//             if (dest->count >= 1 && dest->occupants[0]->color == RED) will_block=1;
//             // select one with highest progress if no captures
//             if (!best_piece || (progress > highest_progress && !will_block)) {
//                     best_piece = p;
//                     best_target = target;
//                     best_capture = 0;
//                     highest_progress = progress;
//                 }
//         }
//     }
//     if (!best_piece) {
//         printf("RED cannot move this turn.\n");
//         return 0;
//     } else {
//         int did_capture;
//         printf("RED moves piece G%d from %i to %i%s\n",
//        best_piece->id,
//        (best_piece->is_in_board) ? 
//         best_piece->board_pos : 0,
//        (best_capture) ? 1 : best_target,
//        (did_capture) ? " and gets a bonus roll!" : "");

//         apply_move(best_piece,die_roll,best_target,best_capture,&did_capture);
//         return did_capture;
//     }

// }


// // DEBUG: Blue
// int play_blue_turn(int die_roll) {
//     Piece* best_piece = NULL;
//     int pieces_on_board = 0;
//     int highest_progress = -1;
//     int best_target = -1;
//     int best_capture = 0;
//     for (int i =0; i <PIECES_PER_PLAYER; i++) {
//         int take_from_base = 0;
//         int to_home = 0;
//         int will_block = 0;
//         Piece *p = &all_pieces[BLUE][i];
//         int target, is_capture;
//         if (can_move_piece(p, die_roll, &target, &is_capture)) {
//             if (p->is_in_board) pieces_on_board++;
//             int progress = (p-> is_in_board) ? (p->remaining_to_home-die_roll) % STD_BOARD_SIZE : (p->is_in_home_path) ? p-> home_index : 0;
        
//             // capture prioritize
//             if (is_capture) {
//                 if (!best_piece || progress > highest_progress) {
//                     best_piece = p;
//                     best_target = target;
//                     best_capture = 1;
//                     highest_progress = progress;
//                 }
//                 continue;
//             }
//             if (p->is_in_home_path) {
//                 best_piece = p;
//                 to_home = 1;
//                 best_capture = 0;
//                 best_target = target;
//                 continue;
//             }
//             // bring in if no capture
//             if (p->is_in_base && die_roll == 6 && pieces_on_board <1 && !is_capture && !to_home) {
//                 Cell *start = &board[START_POSITION[BLUE]];
//                 //if (start->count == 0 || (start->count == 1 && start->occupants[0]->color == BLUE)) will_block=1;
//                 best_piece = p;
//                 best_target = START_POSITION[BLUE];
//                 take_from_base=1;
//                 best_capture=0;
//                 continue;
//             }

//             // avoid blocks 
//             Cell *dest = &board[target];
//             if (dest->count >= 1 && dest->occupants[0]->color == BLUE) will_block=1;
//             // select one with highest progress if no captures
//             if (!best_piece || (progress > highest_progress && !will_block)) {
//                     best_piece = p;
//                     best_target = target;
//                     best_capture = 0;
//                     highest_progress = progress;
//                 }
//         }
//     }
//     if (!best_piece) {
//         printf("BLUE cannot move this turn.\n");
//         return 0;
//     } else {
//         int did_capture;
//         printf("BLUE moves piece B%d from %i to %i%s\n",
//        best_piece->id,
//        (best_piece->is_in_board) ? 
//         best_piece->board_pos : 0,
//        (best_capture) ? 1 : best_target,
//        (did_capture) ? " and gets a bonus roll!" : "");

//         apply_move(best_piece,die_roll,best_target,best_capture,&did_capture);
//         return did_capture;
//     }

// }


// // DEBUG:Yellow
// int play_yellow_turn(int die_roll) {
//     Piece* best_piece = NULL;
//     int pieces_on_board = 0;
//     int highest_progress = -1;
//     int best_target = -1;
//     int best_capture = 0;
//     for (int i =0; i <PIECES_PER_PLAYER; i++) {
//         int take_from_base = 0;
//         int to_home = 0;
//         int will_block = 0;
//         Piece *p = &all_pieces[YELLOW][i];
//         int target, is_capture;
//         if (can_move_piece(p, die_roll, &target, &is_capture)) {
//             if (p->is_in_board) pieces_on_board++;
//             int progress = (p-> is_in_board) ? (p->remaining_to_home-die_roll) % STD_BOARD_SIZE : (p->is_in_home_path) ? p-> home_index : 0;
        
//             // capture prioritize
//             if (is_capture) {
//                 if (!best_piece || progress > highest_progress) {
//                     best_piece = p;
//                     best_target = target;
//                     best_capture = 1;
//                     highest_progress = progress;
//                 }
//                 continue;
//             }
//             if (p->is_in_home_path) {
//                 best_piece = p;
//                 to_home = 1;
//                 best_capture = 0;
//                 best_target = target;
//                 continue;
//             }
//             // bring in if no capture
//             if (p->is_in_base && die_roll == 6 && pieces_on_board <1 && !is_capture && !to_home) {
//                 Cell *start = &board[START_POSITION[YELLOW]];
//                 //if (start->count == 0 || (start->count == 1 && start->occupants[0]->color == YELLOW)) will_block=1;
//                 best_piece = p;
//                 best_target = START_POSITION[YELLOW];
//                 take_from_base=1;
//                 best_capture=0;
//                 continue;
//             }

//             // avoid blocks 
//             Cell *dest = &board[target];
//             if (dest->count >= 1 && dest->occupants[0]->color == YELLOW) will_block=1;
//             // select one with highest progress if no captures
//             if (!best_piece || (progress > highest_progress && !will_block)) {
//                     best_piece = p;
//                     best_target = target;
//                     best_capture = 0;
//                     highest_progress = progress;
//                 }
//         }
//     }
//     if (!best_piece) {
//         printf("YELLOW cannot move this turn.\n");
//         return 0;
//     } else {
//         int did_capture;
//         printf("YELLOW moves piece Y%d from %i to %i%s\n",
//        best_piece->id,
//        (best_piece->is_in_board) ? 
//         best_piece->board_pos : 0,
//        (best_capture) ? 1 : best_target,
//        (did_capture) ? " and gets a bonus roll!" : "");

//         apply_move(best_piece,die_roll,best_target,best_capture,&did_capture);
//         return did_capture;
//     }

// // }





// int play_green_turn(int die_roll) {
//     Piece* best_piece = NULL;
//     int pieces_on_board = 0;
//     int highest_progress = -1;
//     int best_target = -1;
//     int best_capture = 0;
//     for (int i =0; i <PIECES_PER_PLAYER; i++) {
//         int take_from_base = 0;
//         int to_home = 0;
//         int will_block = 0;
//         Piece *p = &all_pieces[GREEN][i];
//         int target, is_capture, path_blocked;
//         if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked)) {
//             if (p->is_in_board) pieces_on_board++;
//             int progress = (p-> is_in_board) ? (p->remaining_to_home-die_roll) : (p->is_in_home_path) ? p-> home_index : 0;
        
//             // capture prioritize
//             if (is_capture) {
//                 if (!best_piece || progress > highest_progress) {
//                     best_piece = p;
//                     best_target = target;
//                     best_capture = 1;
//                     highest_progress = progress;
//                 }
//                 continue;
//             }
//             if (p->is_in_home_path) {
//                 best_piece = p;
//                 to_home = 1;
//                 best_capture = 0;
//                 best_target = target;
//                 continue;
//             }
//             // bring in if no capture
//             if (p->is_in_base && die_roll == 6 && pieces_on_board <1 && !is_capture && !to_home) {
//                 best_piece = p;
//                 best_target = START_POSITION[GREEN];
//                 take_from_base=1;
//                 best_capture=0;
//                 continue;
//             }

//             // avoid blocks 
//             Cell *dest = &board[target];
//             if (dest->count >= 1 && dest->occupants[0]->color == GREEN) will_block=1;
//             // select one with highest progress if no captures
//             if (!best_piece || (progress > highest_progress && !will_block)) {
//                     best_piece = p;
//                     best_target = target;
//                     best_capture = 0;
//                     highest_progress = progress;
//                 }
//         }
//     }
//     if (!best_piece) {
//         printf("GREEN cannot move this turn.\n");
//         return 0;
//     } else {
//         int did_capture;
        
//         // Save initial state BEFORE move
//         int was_in_base = best_piece->is_in_base;
//         int was_in_board = best_piece->is_in_board;
//         int was_in_home_path = best_piece->is_in_home_path;
//         int old_pos = best_piece->board_pos;
//         int old_home_index = best_piece->home_index;

//         apply_move(best_piece, die_roll, best_target, best_capture, &did_capture);

//         // Determine source position for display
//         int src_pos;
//         if (was_in_base) {
//             src_pos = 0;  // base
//         } else if (was_in_board) {
//             src_pos = old_pos;
//         } else if (was_in_home_path) {
//             src_pos = old_home_index;
//         } else {
//             src_pos = -1;  // unknown
//         }

//         printf("GREEN moves piece G%d from %d to %d%s\n",
//             best_piece->id,
//             src_pos,
//             best_target,
//             did_capture ? " and gets a bonus roll!" : "");

//         return did_capture;
//     }
// }






int can_move_piece(Piece *p, int die_roll, int* target_pos, int* is_capture, int* path_blocked) {
    *path_blocked = 0;
    
    // Safety check
    if (!p) {
        if (DEBUG) printf("ERROR: Null piece pointer\n");
        return 0;
    }
    
    if (p->is_in_base) {
        if (die_roll != 6) return 0; // can't come out

        int start = START_POSITION[p->color];
        Cell* cell = &board[start];

        if (cell->count == 0) {
            if (target_pos) *target_pos = start;
            if (is_capture) *is_capture = 0;
            return 1;
        }
        
        // capture if opponent
        if ((cell->count == 1) && (cell->occupants[0]->color != p->color)) {
            if (target_pos) *target_pos = start;
            if (is_capture) *is_capture = 1;
            return 1;
        }
        
        // Can stack with same color pieces
        if (cell->count >= 1 && cell->occupants[0]->color == p->color) {
            if (target_pos) *target_pos = start%STD_BOARD_SIZE;
            if (is_capture) *is_capture = 0;
            return 1;
        }

        return 0; // blocked by opponent
    }

    if (p->is_in_board) {
        // Safety check for board position
        if (p->board_pos < 0 || p->board_pos >= STD_BOARD_SIZE) {
            if (DEBUG) printf("ERROR: Invalid board position %d for piece G%d\n", p->board_pos, p->id);
            return 0;
        }
        // get dest cell
        int new_pos = (p->board_pos + die_roll) % STD_BOARD_SIZE;
        Cell* dest_cell = &board[new_pos];


        // Check if destination is blocked by opponents
        if (dest_cell->count >= 2 && dest_cell->occupants[0]->color != p->color) {
            if (is_capture) *is_capture = 0;
            return 0; // blocked 
        }

        // check for home entry and blocks
        int home_entry = HOME_ENTRY_POSITION[p->color];
        if (DEBUG) printf("DEBUG: Piece %s%d at pos %d, die_roll %d, dist_from_home is %d, dist_from_start is %d, home_entry at %d\n", get_color_name(p->color),
               p->id, p->board_pos, die_roll,p->remaining_to_home-die_roll, p->dist_from_start+die_roll, home_entry);
        
        // Check if the move would cross or land on the home entry position
        int crosses_home_entry = 0;
        int steps_into_home = 0;
        
        for (int i = 0; i <= die_roll; i++) {
            int check_pos = (p->board_pos + i);
            Cell* check_cell = &board[check_pos%STD_BOARD_SIZE];
            
            // If there's a block of 2+ opponent pieces in the path
            if (check_cell->count >= 2 && check_cell->occupants[0]->color != p->color) {
                *path_blocked = 1;
                if ((check_pos-1) <= 0) {
                    Cell *check_previous = &board[((check_pos-1)%STD_BOARD_SIZE)];
                    if (check_previous->count >= 2 && check_previous->occupants[0]->color != p->color) return 0;
                    if (target_pos) *target_pos = ((check_pos-1)%STD_BOARD_SIZE);
                    if (is_capture && check_previous->count>0 && check_previous->occupants[0]->color!=p->color){ // capture if opponent
                        *is_capture = 1;
                    } else if (is_capture) {
                        *is_capture = 0;
                    } 
                    return 1;
                } else {
                    return 0;
                }
            }

            if (check_pos == home_entry || (p->remaining_to_home)-i <= 0) {
                crosses_home_entry = 1;
                steps_into_home = die_roll - i; // Steps remaining after reaching home entry
                if (DEBUG) printf("DEBUG: Crosses home entry at step %d, steps_into_home = %d\n", i, steps_into_home);
                break;
            }
        }
        
        // If crossing home entry, check if we can enter home path
        if (crosses_home_entry && steps_into_home>0) {
            // steps_into_home is the number of steps after reaching home entry position
            if (steps_into_home >= HOME_PATH) {
                if (DEBUG) printf("DEBUG: Would overshoot home path (steps_into_home=%d, HOME_PATH=%d)\n", 
                       steps_into_home, HOME_PATH);
                return 0; // Overshoot home path
            }
            
            if (DEBUG) printf("DEBUG: Entering home path at index %d\n", steps_into_home);
            if (target_pos) *target_pos = (steps_into_home-1) % HOME_PATH; // Home index starts from 0
            if (is_capture) *is_capture = 0;
            return 1;
        }
    


        // Can capture single opponent piece
        if (dest_cell->count == 1 && dest_cell->occupants[0]->color != p->color) {
            if (target_pos) *target_pos = new_pos;
            if (is_capture) *is_capture = 1;
            return 1;
        }

        // Can move to empty space or stack with same color
        if (dest_cell->count == 0 || 
            (dest_cell->count >= 1 && dest_cell->occupants[0]->color == p->color)) {
            if (target_pos) *target_pos = new_pos;
            if (is_capture) *is_capture = 0;
            return 1;
        }

        return 0;
    }

    if (p->is_in_home_path) {
        // Safety check home index
        if (p->home_index < 0 || p->home_index >= HOME_PATH) {
            if (DEBUG) printf("ERROR: Invalid home index %d for piece %s%d\n", p->home_index, get_color_name(p->color),p->id);
            return 0;
        }
        
        if (p->home_index + die_roll == HOME_PATH - 1) {
            // reaching home (final position)
            if (DEBUG) printf("DEBUG: Reaching final home position\n");
            if (target_pos) *target_pos = HOME_PATH;
            if (is_capture) *is_capture = 0;
            return 1;
        }
        
    }
    
    return 0;
}

void apply_move(Piece* p, int die_roll, int target_pos, int is_capture, int* did_capture) {
    *did_capture = 0;
    
    // Safety check
    if (!p) {
        if (DEBUG) printf("ERROR: Null piece pointer in apply_move\n");
        return;
    }

    // from base to start position
    if (p->is_in_base) {
        p->is_in_base = 0;
        p->is_in_board = 1;
        p->board_pos = target_pos;
        p->dist_from_start = 0;  // Just started

        players[p->color].in_base--;
        players[p->color].in_board++;
        players[p->color].last_move = *p;

        Cell* dest_cell = &board[target_pos];
        
        if (is_capture) {
            if (dest_cell->count > 0 && dest_cell->occupants[0]) {
                Piece* victim = dest_cell->occupants[0];
                victim->is_in_base = 1;
                victim->is_in_board = 0;
                victim->board_pos = -1;
                victim->dist_from_start = 0;

                players[victim->color].in_base++;
                players[victim->color].in_board--;

                dest_cell->occupants[0] = p;
                dest_cell->count = 1;
                *did_capture = 1;
            }
        } else {
            dest_cell->occupants[dest_cell->count++] = p;
        }
        return;
    }

    // movement from board
    if (p->is_in_board) {
        // Safety check
        if (p->board_pos < 0 || p->board_pos >= STD_BOARD_SIZE) {
            if (DEBUG) printf("ERROR: Invalid board position %d in apply_move\n", p->board_pos);
            return;
        }
        
        // Remove from old position
        Cell* old_cell = &board[p->board_pos];
        for (int i = 0; i < old_cell->count; i++) {
            if (old_cell->occupants[i] == p) {
                for (int j = i; j < old_cell->count - 1; j++) {
                    old_cell->occupants[j] = old_cell->occupants[j + 1];
                }
                old_cell->count--;
                break;
            }
        }

        int home_entry = HOME_ENTRY_POSITION[p->color];
        
        // Check if entering home path
        int crosses_home_entry = 0;
        int steps_into_home = 0;
        
        for (int i = 0; i <= die_roll; i++) {
            int check_pos = (p->board_pos + i) % STD_BOARD_SIZE;
            if (check_pos == home_entry || p->remaining_to_home <= 0) {
                crosses_home_entry = 1;
                steps_into_home = die_roll - i;
                break;
            }
        }
        
        // Entering home path
        if (crosses_home_entry && steps_into_home>0) {
            if (DEBUG) printf("DEBUG: Applying move to home path, home_index = %d\n", steps_into_home);
            
            // Safety check
            if (steps_into_home <= 0 || steps_into_home >= HOME_PATH) {
                if (DEBUG) printf("ERROR: Invalid home index %d when entering home path\n", steps_into_home);
                return;
            }
            
            p->is_in_board = 0;
            p->is_in_home_path = 1;
            p->board_pos = -1;
            p->home_index = steps_into_home-1;
            p->remaining_to_home = HOME_PATH - steps_into_home - 1; //index 0
            players[p->color].last_move = *p;
            players[p->color].in_board--;
            return;
        }

        // Regular board movement
        int new_pos = (p->board_pos + die_roll) % STD_BOARD_SIZE;
        Cell* dest_cell = &board[new_pos];
        
        if (is_capture) {
            if (dest_cell->count > 0 && dest_cell->occupants[0]) {
                Piece* victim = dest_cell->occupants[0];
                victim->is_in_base = 1;
                victim->is_in_board = 0;
                victim->board_pos = -1;
                victim->dist_from_start = 0;
                players[victim->color].in_board--;
                players[victim->color].in_base++;
                dest_cell->occupants[0] = p;
                dest_cell->count = 1;
                *did_capture = 1;
            }
        } else {
            dest_cell->occupants[dest_cell->count++] = p;
        }
        
        p->board_pos = new_pos;
        players[p->color].last_move = *p;
        p->dist_from_start = p->dist_from_start+die_roll;
        p->remaining_to_home = p->remaining_to_home-die_roll;
        return;
    }

    // movement within home path
    if (p->is_in_home_path) {

        
        if (target_pos == HOME_PATH) {
            // Piece has reached home
            if (DEBUG) printf("DEBUG: Piece %s%d reaches final home\n",get_color_name(p->color), p->id);
            p->home_index = -1;
            p->is_in_home = 1;
            p->remaining_to_home = 0;
            p->is_in_home_path = 0;
            p -> board_pos = -1;
            p -> is_in_board = 0;
            p -> is_in_base = 0;
            p -> dist_from_start = STD_BOARD_SIZE;

            players[p->color].last_move = *p;
            players[p->color].in_home++;
            return;
        }

    }
}


        // FIXED: Add logic for regular home path movement
    //     if (p->home_index + die_roll < HOME_PATH - 1) {
    //         if (target_pos) *target_pos = p->home_index + die_roll;
    //         if (is_capture) *is_capture = 0;
    //         return 1;
    //     }
    }

            // FIXED: Add regular home path movement
        // if (target_pos < HOME_PATH) {
        //     p->home_index = target_pos;
        //     p->remaining_to_home = HOME_PATH - target_pos - 1;
        //     players[p->color].last_move = *p;
        //     return;
        // }


older home path logic in apply_move


        // int home_entry = HOME_ENTRY_POSITION[p->color];
        
        // // Check if entering home path
        // int crosses_home_entry = 0;
        // int steps_into_home = 0;
        
        // for (int i = 0; i <= die_roll; i++) {
        //     // int check_pos = (p->board_pos + i) % STD_BOARD_SIZE;
        //     // check_pos == home_entry ||
        //     if ( (p->remaining_to_home - i) <= 0) {
        //         crosses_home_entry = 1;
        //         steps_into_home = die_roll - i;
        //         break;
        //     }
        // }
        
        // Entering home path
        // if (crosses_home_entry && steps_into_home > 0) {
        // if (DEBUG) printf("DEBUG: Applying move to home path, home_index = %d\n", steps_into_home - 1);
        //     // Safety check
        //     if (steps_into_home <= 0 || steps_into_home > HOME_PATH) {
        //         if (DEBUG) printf("ERROR: Invalid home index %d when entering home path\n", steps_into_home);
        //         return;
        //     }
            
        //     p->is_in_board = 0;
        //     p->is_in_home_path = 1;
        //     p->board_pos = -1;
        //     p->home_index = steps_into_home - 1;
        //     p->remaining_to_home = HOME_PATH - steps_into_home;
        //     players[p->color].last_move = p;
        //     players[p->color].in_board--;
        //     return;
        // }
