# Changes Documentation

## Overview
This document details the step-by-step changes made to implement consistent outcome tracking and printing logic across all player turn functions in the Ludo game. The goal was to apply the same pattern used in `play_turn_red` to all other player turn functions (`play_green_turn`, `play_yellow_turn`, and `play_blue_turn`).

## Analysis Phase

### 1. Initial Code Examination
- Examined the existing `play_turn_red` function to understand the outcome tracking pattern
- Identified the key components:
  - `Outcomes outcome;` variable declaration
  - `Outcomes _outcome;` variable for tracking individual move outcomes
  - Outcome assignments for different move types
  - `print_outcome` function call with appropriate parameters

### 2. Pattern Identification
The `play_turn_red` function used the following pattern:
```c
Outcomes outcome; // for print
// ... in the priority loop:
Outcomes _outcome;
if (condition) {
    _outcome = SPECIFIC_OUTCOME;
}
// ... when selecting best piece:
outcome = _outcome;
// ... when no move possible:
outcome = NON;
// ... at the end:
print_outcome(outcome, &players[RED], old_pos, best_target, ...);
```

## Implementation Phase

### 3. Updated `play_green_turn` Function

#### 3.1 Added outcome variable declaration
```c
Outcomes outcome; // for print
```

#### 3.2 Added _outcome variable in priority loop
```c
Outcomes _outcome;
```

#### 3.3 Added outcome assignments for each priority case
- Block moves: `_outcome = ST_PATH;`
- Base to start: `_outcome = FR_BASE;`
- Home path moves: `_outcome = H_PATH;` (with HOME check)
- Board moves: `_outcome = ST_PATH;`
- Captures: `_outcome = CAPTURE;`

#### 3.4 Added outcome assignment when selecting best piece
```c
outcome = _outcome;
```

#### 3.5 Added NON outcome for no moves
```c
outcome = NON;
```

#### 3.6 Added print_outcome function call
```c
print_outcome(outcome, &players[GREEN], old_pos, best_target, 
              (did_capture) ? players[GREEN].last_victim : NULL, 
              (best_target >= 0 && best_target < STD_BOARD_SIZE) ? &board[best_target] : NULL);
```

### 4. Updated `play_yellow_turn` Function

#### 4.1 Added outcome variable declaration
```c
Outcomes outcome; // for print
```

#### 4.2 Added _outcome variable and assignments
- Base to start: `_outcome = FR_BASE;`
- Home path moves: `_outcome = H_PATH;` (with HOME check)
- Captures: `_outcome = CAPTURE;`
- Board moves: `_outcome = ST_PATH;`

#### 4.3 Added outcome flow control
- `outcome = _outcome;` when selecting best piece
- `outcome = NON;` when no moves possible

#### 4.4 Added print_outcome function call
```c
print_outcome(outcome, &players[YELLOW], old_pos, best_target, 
              (did_capture) ? players[YELLOW].last_victim : NULL, 
              (best_target >= 0 && best_target < STD_BOARD_SIZE) ? &board[best_target] : NULL);
```

### 5. Updated `play_blue_turn` Function

#### 5.1 Added outcome variable declaration
```c
Outcomes outcome; // for print
```

#### 5.2 Added _outcome variable and logic
Since blue player uses a simple first-available strategy, added outcome determination based on move type:
```c
Outcomes _outcome;
if (p->is_in_base && die_roll == 6) {
    _outcome = FR_BASE;
} else if (p->is_in_home_path) {
    if (target == HOME_PATH) {
        _outcome = HOME;
    } else {
        _outcome = H_PATH;
    }
} else if (is_capture) {
    _outcome = CAPTURE;
} else if (p->is_in_board) {
    _outcome = ST_PATH;
} else {
    _outcome = ST_PATH; // default
}
```

#### 5.3 Added outcome flow control
- `outcome = _outcome;` when selecting piece
- `outcome = NON;` when no moves possible

#### 5.4 Added print_outcome function call
```c
print_outcome(outcome, &players[BLUE], old_pos, best_target, 
              (did_capture) ? players[BLUE].last_victim : NULL, 
              (best_target >= 0 && best_target < STD_BOARD_SIZE) ? &board[best_target] : NULL);
```

## Bug Fixes and Safety Improvements

### 6. Added HOME Outcome Support
For all functions, added logic to detect when pieces reach the final home position:
```c
if (target == HOME_PATH) {
    _outcome = HOME;
} else {
    _outcome = H_PATH;
}
```

### 7. Fixed Array Bounds Issues
Added safety checks in print_outcome calls to prevent accessing invalid board positions:
```c
(best_target >= 0 && best_target < STD_BOARD_SIZE) ? &board[best_target] : NULL
```

### 8. Fixed NULL Pointer Issues in print_outcome Function
Added safety checks for all cases that access `player->last_move`:

#### 8.1 FR_BASE case
```c
if (player->last_move) {
    printf("[%s] player moves piece %c%d to the starting point.\n", ...);
    // ... rest of output
}
```

#### 8.2 ST_PATH case
```c
if (player->last_move) {
    printf("[%s] moves piece %c%d from location %d to %d...\n", ...);
}
```

#### 8.3 BLOCKED case
```c
if (player->last_move && dest_cell && dest_cell->occupants[0]) {
    printf("[%s] piece %c%d is blocked...\n", ...);
}
```

#### 8.4 CAPTURE case
```c
if (victim && player->last_victim && player->last_move) {
    printf("[%s] piece %c%d lands on square %d, captures...\n", ...);
    // ... rest of output
}
```

#### 8.5 H_PATH case
```c
if (player->last_move) {
    printf("[%s] piece %c%d enters the home path...\n", ...);
}
```

#### 8.6 HOME case
```c
if (player->last_move) {
    printf("[%s] piece %c%d reaches home!\n", ...);
    printf("[%s] player now has %d/4 pieces at home.\n", ...);
}
```

### 9. Fixed Format String Warning
Changed format specifier from `%s` to `%c` for single character output:
```c
// Before: get_color_name(color)[0] with %s
// After: get_color_name(color)[0] with %c
```

## Testing and Validation

### 10. Compilation Testing
- Fixed all compilation warnings and errors
- Ensured clean compilation with `-Wall -Wextra` flags

### 11. Runtime Testing
- Tested game execution to ensure no segmentation faults
- Verified that all outcome types are properly displayed
- Confirmed consistent output format across all player colors

## Results

### 12. Achieved Consistency
All player turn functions now follow the exact same pattern:
1. ✅ `Outcomes outcome;` variable declaration
2. ✅ `Outcomes _outcome;` variable for individual move tracking
3. ✅ Proper outcome assignments for all move types
4. ✅ `outcome = _outcome;` when selecting best piece
5. ✅ `outcome = NON;` when no moves possible
6. ✅ `print_outcome` function call with appropriate parameters

### 13. Enhanced Game Output
The game now provides rich, detailed output for all moves including:
- Move descriptions with piece IDs and positions
- Capture events with victim information
- Home path entries and final home arrivals
- Current piece status for each player after every move
- Consistent formatting across all player colors

## Files Modified
- `logic.c` - Main implementation file containing all player turn functions and print_outcome function

## Summary
Successfully implemented consistent outcome tracking and printing logic across all player turn functions, maintaining the exact same structure and variable naming conventions as used in `play_turn_red`. The implementation includes proper safety checks, handles all outcome types, and provides a rich gaming experience with detailed move descriptions.
