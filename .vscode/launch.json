{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Ludo",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/ludo",
      "args": [],
      "stopAtEntry": false,
      "cwd": "${workspaceFolder}",
      "environment": [],
      "externalConsole": false,
      "MIMode": "gdb",
      "miDebuggerPath": "/opt/homebrew/bin/gdb",
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        }
      ],
      "preLaunchTask": "build"
    },
    {
      "type": "pwa-msedge",
      "name": "Launch Microsoft Edge",
      "request": "launch",
      "runtimeArgs": [
        "--remote-debugging-port=9222"
      ],
      "url": "/Users/<USER>/.vscode/extensions/ms-edgedevtools.vscode-edge-devtools-2.1.9/out/startpage/index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    },
    {
      "type": "pwa-msedge",
      "name": "Launch Microsoft Edge in headless mode",
      "request": "launch",
      "runtimeArgs": [
        "--headless",
        "--remote-debugging-port=9222"
      ],
      "url": "/Users/<USER>/.vscode/extensions/ms-edgedevtools.vscode-edge-devtools-2.1.9/out/startpage/index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    },
    {
      "type": "vscode-edge-devtools.debug",
      "name": "Open Edge DevTools",
      "request": "attach",
      "url": "/Users/<USER>/.vscode/extensions/ms-edgedevtools.vscode-edge-devtools-2.1.9/out/startpage/index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    }
  ],
  "compounds": [
    {
      "name": "Launch Edge Headless and attach DevTools",
      "configurations": [
        "Launch Microsoft Edge in headless mode",
        "Open Edge DevTools"
      ]
    },
    {
      "name": "Launch Edge and attach DevTools",
      "configurations": [
        "Launch Microsoft Edge",
        "Open Edge DevTools"
      ]
    }
  ]
}