#ifndef TYPES_H
#define TYPES_H

// debug
#define DEBUG 1

// constats

#define NUM_PLAYERS 4
#define PIECES_PER_PLAYER 4
#define STD_BOARD_SIZE 52
#define HOME_PATH 5
#define TOTAL_LENGTH 58


// Color
typedef enum {
  YELLOW = 0,
  BLUE,
  RED,
  GRE<PERSON>,
} Color;

typedef enum {
    FR_BASE=0,
    ST_PATH,
    BLOCKED,
    CAPTURE,
    H_PATH,
    HOME,
    NON,
} Outcomes;


typedef enum {
  CLOCKWISE=1,
  ANTICLOCKWISE=0,
  UNKNOWN=-1
} Direction;

// Piece
typedef struct {
  Color color; // owner
  int id;   // piece id
  int is_in_base;   //1 if in base
  int is_in_board; //is in track
  int is_in_home;  //is reached home
  int is_in_home_path;
  int board_pos; // 0 - 51 if on board
  int home_index; //0-4 if in home track
  Direction direction;

  int dist_from_start; // 0-51 from start
  int remaining_to_home; // remaining steps 

  int captures;
} Piece;

typedef struct {
  Color color;
  int id;
  int in_home;
  int in_base;
  int in_board;
  Piece* last_move;
  int order;
  Piece* last_victim;
  Cell* blocks[PIECES_PER_PLAYER];
} Player;


// cell structure
typedef struct {
  int count; //how many peices in cell
  Piece* occupants[PIECES_PER_PLAYER]; //pointers to pieces on this square
  int superblock;
  Direction block_direction;
} Cell;

// External globals
extern Piece all_pieces[NUM_PLAYERS][PIECES_PER_PLAYER]; //all 16 pieces
extern Cell board[STD_BOARD_SIZE]; //main track
extern const int START_POSITION[NUM_PLAYERS];  //where each color starts
extern const int HOME_ENTRY_POSITION[NUM_PLAYERS];
extern Player players[NUM_PLAYERS];

// optimal helpers
const char* get_color_name(Color c); // for printing
void initialize_pieces();
void initialize_board();
void initialize_players();
const char* get_direction(Direction d);

#endif
