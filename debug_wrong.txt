<PERSON><PERSON><PERSON> moves piece G1 from 26 to 31
Green rolls 2
DEBUG: Piece Green1 at pos 31, die_roll 2, dist_from_home is 32, dist_from_start is 19, home_entry at 12
G<PERSON><PERSON> moves piece G1 from 31 to 33
Green rolls 4
DEBUG: Piece Green1 at pos 33, die_roll 4, dist_from_home is 28, dist_from_start is 23, home_entry at 12
<PERSON><PERSON><PERSON> moves piece G1 from 33 to 37
Green rolls 6
DEBUG: Piece Green1 at pos 37, die_roll 6, dist_from_home is 22, dist_from_start is 29, home_entry at 12
G<PERSON><PERSON> moves piece G2 from 0 to 14
Green rolls 2
DEBUG: Piece Green1 at pos 37, die_roll 2, dist_from_home is 26, dist_from_start is 25, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
G<PERSON><PERSON> moves piece G1 from 37 to 39
Green rolls 2
DEBUG: Piece Green1 at pos 39, die_roll 2, dist_from_home is 24, dist_from_start is 27, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
<PERSON><PERSON><PERSON> moves piece G1 from 39 to 41
Green rolls 6
DEBUG: Piece Green1 at pos 41, die_roll 6, dist_from_home is 18, dist_from_start is 33, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
GREEN moves piece G3 from 0 to 14
Green rolls 6
DEBUG: Piece Green1 at pos 41, die_roll 6, dist_from_home is 18, dist_from_start is 33, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
GREEN moves piece G4 from 0 to 14
Green rolls 5
DEBUG: Piece Green1 at pos 41, die_roll 5, dist_from_home is 19, dist_from_start is 32, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
GREEN moves piece G1 from 41 to 46
Green rolls 5
DEBUG: Piece Green1 at pos 46, die_roll 5, dist_from_home is 14, dist_from_start is 37, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
GREEN moves piece G1 from 46 to 51
Green rolls 2
DEBUG: Piece Green1 at pos 51, die_roll 2, dist_from_home is 12, dist_from_start is 39, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
GREEN moves piece G1 from 51 to 1
Green rolls 1
DEBUG: Piece Green1 at pos 1, die_roll 1, dist_from_home is 11, dist_from_start is 40, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
GREEN moves piece G1 from 1 to 2
Green rolls 2
DEBUG: Piece Green1 at pos 2, die_roll 2, dist_from_home is 9, dist_from_start is 42, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
GREEN moves piece G1 from 2 to 4
Green rolls 1
DEBUG: Piece Green1 at pos 4, die_roll 1, dist_from_home is 8, dist_from_start is 43, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
GREEN moves piece G1 from 4 to 5
Green rolls 1
DEBUG: Piece Green1 at pos 5, die_roll 1, dist_from_home is 7, dist_from_start is 44, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
GREEN moves piece G1 from 5 to 6
Green rolls 3
DEBUG: Piece Green1 at pos 6, die_roll 3, dist_from_home is 4, dist_from_start is 47, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
GREEN moves piece G1 from 6 to 9
Green rolls 2
DEBUG: Piece Green1 at pos 9, die_roll 2, dist_from_home is 2, dist_from_start is 49, home_entry at 12
DEBUG: Piece Green2 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
GREEN moves piece G1 from 9 to 11
Green rolls 4
DEBUG: Piece Green1 at pos 11, die_roll 4, dist_from_home is -2, dist_from_start is 53, home_entry at 12
DEBUG: Crosses home entry at step 1, steps_into_home = 3
DEBUG: Entering home path at index 3
DEBUG: Piece Green2 at pos 14, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 12
DEBUG: Applying move to home path, home_index = 3
GREEN moves piece G1 from 11 to 2
Green rolls 4
DEBUG: Piece Green2 at pos 14, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 12
GREEN moves piece G2 from 14 to 18
Green rolls 3
DEBUG: Piece Green2 at pos 18, die_roll 3, dist_from_home is 44, dist_from_start is 7, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
GREEN moves piece G2 from 18 to 21
Green rolls 2
DEBUG: Reaching final home position
DEBUG: Piece Green2 at pos 21, die_roll 2, dist_from_home is 42, dist_from_start is 9, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green1 reaches final home
GREEN moves piece G1 from 2 to 5
Green rolls 2
DEBUG: Piece Green2 at pos 21, die_roll 2, dist_from_home is 42, dist_from_start is 9, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
GREEN moves piece G2 from 21 to 23
Green rolls 1
DEBUG: Piece Green2 at pos 23, die_roll 1, dist_from_home is 41, dist_from_start is 10, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
GREEN moves piece G2 from 23 to 24
Green rolls 5
DEBUG: Piece Green2 at pos 24, die_roll 5, dist_from_home is 36, dist_from_start is 15, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
GREEN moves piece G2 from 24 to 29
Green rolls 3
DEBUG: Piece Green2 at pos 29, die_roll 3, dist_from_home is 33, dist_from_start is 18, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
GREEN moves piece G2 from 29 to 32
Green rolls 6
DEBUG: Piece Green2 at pos 32, die_roll 6, dist_from_home is 27, dist_from_start is 24, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
GREEN moves piece G2 from 32 to 38
Green rolls 1
DEBUG: Piece Green2 at pos 38, die_roll 1, dist_from_home is 26, dist_from_start is 25, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
GREEN moves piece G2 from 38 to 39
Green rolls 6
DEBUG: Piece Green2 at pos 39, die_roll 6, dist_from_home is 20, dist_from_start is 31, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
GREEN moves piece G2 from 39 to 45
Green rolls 3
DEBUG: Piece Green2 at pos 45, die_roll 3, dist_from_home is 17, dist_from_start is 34, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
GREEN moves piece G2 from 45 to 48
Green rolls 5
DEBUG: Piece Green2 at pos 48, die_roll 5, dist_from_home is 12, dist_from_start is 39, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
GREEN moves piece G2 from 48 to 1
Green rolls 2
DEBUG: Piece Green2 at pos 1, die_roll 2, dist_from_home is 10, dist_from_start is 41, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 12
GREEN moves piece G2 from 1 to 3
Green rolls 3
DEBUG: Piece Green2 at pos 3, die_roll 3, dist_from_home is 7, dist_from_start is 44, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
GREEN moves piece G2 from 3 to 6
Green rolls 1
DEBUG: Piece Green2 at pos 6, die_roll 1, dist_from_home is 6, dist_from_start is 45, home_entry at 12
DEBUG: Piece Green3 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
GREEN moves piece G2 from 6 to 7
Green rolls 6
DEBUG: Piece Green2 at pos 7, die_roll 6, dist_from_home is 0, dist_from_start is 51, home_entry at 12
DEBUG: Crosses home entry at step 5, steps_into_home = 1
DEBUG: Entering home path at index 1
DEBUG: Piece Green3 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 12
DEBUG: Applying move to home path, home_index = 1
GREEN moves piece G2 from 7 to 0
Green rolls 3
DEBUG: Piece Green3 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 12
GREEN moves piece G3 from 14 to 17
Green rolls 1
DEBUG: Piece Green3 at pos 17, die_roll 1, dist_from_home is 47, dist_from_start is 4, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
GREEN moves piece G3 from 17 to 18
Green rolls 1
DEBUG: Piece Green3 at pos 18, die_roll 1, dist_from_home is 46, dist_from_start is 5, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 12
GREEN moves piece G3 from 18 to 19
Green rolls 5
DEBUG: Piece Green3 at pos 19, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 12
DEBUG: Piece Green4 at pos 14, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 12
GREEN moves piece G4 from 14 to 19
Green rolls 5
DEBUG: Piece Green3 at pos 19, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 12
GREEN moves piece G3 from 19 to 24
Green rolls 6
DEBUG: Piece Green3 at pos 24, die_roll 6, dist_from_home is 35, dist_from_start is 16, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 12
GREEN moves piece G3 from 24 to 30
Green rolls 4
DEBUG: Reaching final home position
DEBUG: Piece Green3 at pos 30, die_roll 4, dist_from_home is 31, dist_from_start is 20, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 4, dist_from_home is 42, dist_from_start is 9, home_entry at 12
DEBUG: Piece Green2 reaches final home
GREEN moves piece G2 from 0 to 5
Green rolls 1
DEBUG: Piece Green3 at pos 30, die_roll 1, dist_from_home is 34, dist_from_start is 17, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 1, dist_from_home is 45, dist_from_start is 6, home_entry at 12
GREEN moves piece G3 from 30 to 31
Green rolls 4
DEBUG: Piece Green3 at pos 31, die_roll 4, dist_from_home is 30, dist_from_start is 21, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 4, dist_from_home is 42, dist_from_start is 9, home_entry at 12
GREEN moves piece G3 from 31 to 35
Green rolls 5
DEBUG: Piece Green3 at pos 35, die_roll 5, dist_from_home is 25, dist_from_start is 26, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 12
GREEN moves piece G3 from 35 to 40
Green rolls 6
DEBUG: Piece Green3 at pos 40, die_roll 6, dist_from_home is 19, dist_from_start is 32, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 12
GREEN moves piece G3 from 40 to 46
Green rolls 3
DEBUG: Piece Green3 at pos 46, die_roll 3, dist_from_home is 16, dist_from_start is 35, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 3, dist_from_home is 43, dist_from_start is 8, home_entry at 12
GREEN moves piece G3 from 46 to 49
Green rolls 4
DEBUG: Piece Green3 at pos 49, die_roll 4, dist_from_home is 12, dist_from_start is 39, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 4, dist_from_home is 42, dist_from_start is 9, home_entry at 12
GREEN moves piece G3 from 49 to 1
Green rolls 5
DEBUG: Piece Green3 at pos 1, die_roll 5, dist_from_home is 7, dist_from_start is 44, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 12
GREEN moves piece G3 from 1 to 6
Green rolls 1
DEBUG: Piece Green3 at pos 6, die_roll 1, dist_from_home is 6, dist_from_start is 45, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 1, dist_from_home is 45, dist_from_start is 6, home_entry at 12
GREEN moves piece G3 from 6 to 7
Green rolls 2
DEBUG: Piece Green3 at pos 7, die_roll 2, dist_from_home is 4, dist_from_start is 47, home_entry at 12
DEBUG: Piece Green4 at pos 19, die_roll 2, dist_from_home is 44, dist_from_start is 7, home_entry at 12
GREEN moves piece G3 from 7 to 9
Green rolls 5
DEBUG: Piece Green3 at pos 9, die_roll 5, dist_from_home is -1, dist_from_start is 52, home_entry at 12
DEBUG: Crosses home entry at step 3, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Piece Green4 at pos 19, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 12
DEBUG: Applying move to home path, home_index = 2
GREEN moves piece G3 from 9 to 1
Green rolls 1
DEBUG: Piece Green4 at pos 19, die_roll 1, dist_from_home is 45, dist_from_start is 6, home_entry at 12
GREEN moves piece G4 from 19 to 20
Green rolls 4
DEBUG: Piece Green4 at pos 20, die_roll 4, dist_from_home is 41, dist_from_start is 10, home_entry at 12
GREEN moves piece G4 from 20 to 24
Green rolls 4
DEBUG: Piece Green4 at pos 24, die_roll 4, dist_from_home is 37, dist_from_start is 14, home_entry at 12
GREEN moves piece G4 from 24 to 28
Green rolls 6
DEBUG: Piece Green4 at pos 28, die_roll 6, dist_from_home is 31, dist_from_start is 20, home_entry at 12
GREEN moves piece G4 from 28 to 34
Green rolls 3
DEBUG: Reaching final home position
DEBUG: Piece Green4 at pos 34, die_roll 3, dist_from_home is 28, dist_from_start is 23, home_entry at 12
DEBUG: Piece Green3 reaches final home
GREEN moves piece G3 from 1 to 5
Green rolls 1
DEBUG: Piece Green4 at pos 34, die_roll 1, dist_from_home is 30, dist_from_start is 21, home_entry at 12
GREEN moves piece G4 from 34 to 35
Green rolls 4
DEBUG: Piece Green4 at pos 35, die_roll 4, dist_from_home is 26, dist_from_start is 25, home_entry at 12
GREEN moves piece G4 from 35 to 39
Green rolls 3
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 23, dist_from_start is 28, home_entry at 12
GREEN moves piece G4 from 39 to 42
Green rolls 3
DEBUG: Piece Green4 at pos 42, die_roll 3, dist_from_home is 20, dist_from_start is 31, home_entry at 12
GREEN moves piece G4 from 42 to 45
Green rolls 3
DEBUG: Piece Green4 at pos 45, die_roll 3, dist_from_home is 17, dist_from_start is 34, home_entry at 12
GREEN moves piece G4 from 45 to 48
Green rolls 1
DEBUG: Piece Green4 at pos 48, die_roll 1, dist_from_home is 16, dist_from_start is 35, home_entry at 12
GREEN moves piece G4 from 48 to 49
Green rolls 4
DEBUG: Piece Green4 at pos 49, die_roll 4, dist_from_home is 12, dist_from_start is 39, home_entry at 12
GREEN moves piece G4 from 49 to 1
Green rolls 5
DEBUG: Piece Green4 at pos 1, die_roll 5, dist_from_home is 7, dist_from_start is 44, home_entry at 12
GREEN moves piece G4 from 1 to 6
Green rolls 1
DEBUG: Piece Green4 at pos 6, die_roll 1, dist_from_home is 6, dist_from_start is 45, home_entry at 12
GREEN moves piece G4 from 6 to 7
Green rolls 2
DEBUG: Piece Green4 at pos 7, die_roll 2, dist_from_home is 4, dist_from_start is 47, home_entry at 12
GREEN moves piece G4 from 7 to 9
Green rolls 3
DEBUG: Piece Green4 at pos 9, die_roll 3, dist_from_home is 1, dist_from_start is 50, home_entry at 12
DEBUG: Crosses home entry at step 3, steps_into_home = 0
GREEN moves piece G4 from 9 to 12
Green rolls 4
DEBUG: Piece Green4 at pos 12, die_roll 4, dist_from_home is -3, dist_from_start is 54, home_entry at 12
GREEN moves piece G4 from 12 to 16
Green rolls 5
DEBUG: Piece Green4 at pos 16, die_roll 5, dist_from_home is -8, dist_from_start is 59, home_entry at 12
GREEN moves piece G4 from 16 to 21
Green rolls 2
DEBUG: Piece Green4 at pos 21, die_roll 2, dist_from_home is -10, dist_from_start is 61, home_entry at 12
GREEN moves piece G4 from 21 to 23
Green rolls 3
DEBUG: Piece Green4 at pos 23, die_roll 3, dist_from_home is -13, dist_from_start is 64, home_entry at 12
GREEN moves piece G4 from 23 to 26
Green rolls 2
DEBUG: Piece Green4 at pos 26, die_roll 2, dist_from_home is -15, dist_from_start is 66, home_entry at 12
GREEN moves piece G4 from 26 to 28
Green rolls 3
DEBUG: Piece Green4 at pos 28, die_roll 3, dist_from_home is -18, dist_from_start is 69, home_entry at 12
GREEN moves piece G4 from 28 to 31
Green rolls 4
DEBUG: Piece Green4 at pos 31, die_roll 4, dist_from_home is -22, dist_from_start is 73, home_entry at 12
GREEN moves piece G4 from 31 to 35
Green rolls 1
DEBUG: Piece Green4 at pos 35, die_roll 1, dist_from_home is -23, dist_from_start is 74, home_entry at 12
GREEN moves piece G4 from 35 to 36
Green rolls 2
DEBUG: Piece Green4 at pos 36, die_roll 2, dist_from_home is -25, dist_from_start is 76, home_entry at 12
GREEN moves piece G4 from 36 to 38
Green rolls 6
DEBUG: Piece Green4 at pos 38, die_roll 6, dist_from_home is -31, dist_from_start is 82, home_entry at 12
GREEN moves piece G4 from 38 to 44
Green rolls 3
DEBUG: Piece Green4 at pos 44, die_roll 3, dist_from_home is -34, dist_from_start is 85, home_entry at 12
GREEN moves piece G4 from 44 to 47
Green rolls 3
DEBUG: Piece Green4 at pos 47, die_roll 3, dist_from_home is -37, dist_from_start is 88, home_entry at 12
GREEN moves piece G4 from 47 to 50
Green rolls 2
DEBUG: Piece Green4 at pos 50, die_roll 2, dist_from_home is -39, dist_from_start is 90, home_entry at 12
GREEN moves piece G4 from 50 to 0
Green rolls 1
DEBUG: Piece Green4 at pos 0, die_roll 1, dist_from_home is -40, dist_from_start is 91, home_entry at 12
GREEN moves piece G4 from 0 to 1
Green rolls 2
DEBUG: Piece Green4 at pos 1, die_roll 2, dist_from_home is -42, dist_from_start is 93, home_entry at 12
GREEN moves piece G4 from 1 to 3
Green rolls 3
DEBUG: Piece Green4 at pos 3, die_roll 3, dist_from_home is -45, dist_from_start is 96, home_entry at 12
GREEN moves piece G4 from 3 to 6
Green rolls 5
DEBUG: Piece Green4 at pos 6, die_roll 5, dist_from_home is -50, dist_from_start is 101, home_entry at 12
GREEN moves piece G4 from 6 to 11
Green rolls 5
DEBUG: Piece Green4 at pos 11, die_roll 5, dist_from_home is -55, dist_from_start is 106, home_entry at 12
DEBUG: Crosses home entry at step 1, steps_into_home = 4
DEBUG: Entering home path at index 4
DEBUG: Applying move to home path, home_index = 4
GREEN moves piece G4 from 11 to 3
Green rolls 5
GREEN cannot move this turn.
Green rolls 5
GREEN cannot move this turn.
Green rolls 2
GREEN cannot move this turn.
Green rolls 4
GREEN cannot move this turn.
Green rolls 1
DEBUG: Reaching final home position
DEBUG: Piece Green4 reaches final home
GREEN moves piece G4 from 3 to 5
Player Green won
