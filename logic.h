#ifndef LOGIC_H
#define LOGIC_H
#include "types.h"

// roll a six faced die
int roll_die();
int check_win(Color c);
int play_red_turn(int die_roll);
int play_blue_turn(int die_roll);
int play_green_turn(int die_roll);
int play_yellow_turn(int die_roll);

// print outcome function
void print_outcome(Outcomes outcome, Player* player, int source_pos, int dest_pos, Piece* victim, Cell* dest_cell, int original_target, int was_blocked, int die_roll);

#endif