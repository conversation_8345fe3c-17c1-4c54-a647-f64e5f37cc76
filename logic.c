
// TODO: Add blocked info to print_outcome and add CS-Rules
// CS-Rules Implementation:
// CS-3: Block Formation and Movement Restriction
// CS-4: Block Movement Direction
// CS-5: Block Direction Restoration
// CS-8: Block Capture
// CS-9: Piece Reset on Capture

#include <stdlib.h>
#include "logic.h"
#include "types.h"
#include <stdio.h>
#include <stdbool.h>

int roll_die() {
    return (rand() % 6) + 1; // 1-6
}

int flip_coin() {
    return (rand() % 2);
}

// return 1 if the given color has all pieces in home
int check_win(Color c) {
    for(int i = 0; i <PIECES_PER_PLAYER; i++) {
        if (!all_pieces[c][i].is_in_home) return 0;
    }
    return 1;
}



void print_outcome(Outcomes outcome, Player* player, int source_pos, int dest_pos, Piece* victim, Cell* dest_cell, int original_target, int was_blocked, int die_roll) {
    Color color = player->color;
    const char* color_name = get_color_name(color);

    switch (outcome) {
        case FR_BASE:
            if (player->last_move) {
                // Check if this was a capture from base
                if (victim && player->last_victim) {
                    printf("CAPTURE: [%s] player moves piece %c%d to the starting point and captures [%s] piece %c%d.\n",
                           color_name,
                           get_color_name(color)[0],
                           player->last_move->id,
                           get_color_name(player->last_victim->color),
                           get_color_name(player->last_victim->color)[0],
                           player->last_victim->id);
                } else {
                    printf("[%s] player moves piece %c%d to the starting point.\n",
                           color_name,
                           get_color_name(color)[0],
                           player->last_move->id);
                }
                printf("[%s] player now has %d/4 pieces on the board and %d/4 pieces on the base.\n",
                       color_name,
                       player->in_board,
                       player->in_base);
            }
            break;

        case ST_PATH:
            if (player->last_move) {
                // Check if this move was blocked
                if (was_blocked && original_target != dest_pos) {
                    printf("BLOCK: [%s] piece %c%d was blocked - moved from L%d to L%d instead of intended L%d (blocked by opponent pieces).\n",
                           color_name,
                           get_color_name(color)[0],
                           player->last_move->id,
                           source_pos,
                           dest_pos,
                           original_target);
                } else {
                    printf("[%s] moves piece %c%d from location L%d to L%d by %d units in %s direction.\n",
                           color_name,
                           get_color_name(color)[0],
                           player->last_move->id,
                           source_pos,
                           dest_pos,
                           dest_pos - source_pos > 0 ? dest_pos - source_pos : dest_pos - source_pos + STD_BOARD_SIZE,
                            get_direction(player->last_move->direction));
                }
            }
            break;

        case BLOCKED:
            if (player->last_move && dest_cell && dest_cell->occupants[0]) {
                printf("BLOCK: [%s] piece %c%d is fully blocked from moving from %d to %d by [%s] piece %c%d.\n",
                       color_name,
                       get_color_name(color)[0],
                       player->last_move->id,
                       source_pos,
                       dest_pos,
                       get_color_name(dest_cell->occupants[0]->color),
                       get_color_name(dest_cell->occupants[0]->color)[0],
                       dest_cell->occupants[0]->id);
            }

            if (player->in_board <= 1) {
                printf("[%s] does not have other pieces in the board to move instead of the blocked piece.\n",
                       color_name);
                printf("Ignoring the throw and moving on to the next player.\n");
            } else {
                printf("[%s] does not have other pieces in the board to move instead of the blocked piece.\n",
                       color_name);
                printf("Moved the piece to square %d which is the cell before the block.\n",
                       source_pos);
            }
            break;

        case CAPTURE:
            if (victim && player->last_victim && player->last_move) {
                // Determine capture context
                if (player->last_move->is_in_board) {
                    printf("CAPTURE: [%s] piece %c%d lands on square %d, captures [%s] piece %c%d, and returns it to the base.\n",
                           color_name,
                           get_color_name(color)[0],
                           player->last_move->id,
                           dest_pos,
                           get_color_name(player->last_victim->color),
                           get_color_name(player->last_victim->color)[0],
                           player->last_victim->id);
                } else {
                    printf("CAPTURE: [%s] piece %c%d lands on square %d, captures [%s] piece %c%d, and returns it to the base.\n",
                           color_name,
                           get_color_name(color)[0],
                           player->last_move->id,
                           dest_pos,
                           get_color_name(player->last_victim->color),
                           get_color_name(player->last_victim->color)[0],
                           player->last_victim->id);
                }

                printf("[%s] player now has %d/4 pieces on the board and %d/4 pieces on the base.\n",
                       get_color_name(player->last_victim->color),
                       players[player->last_victim->color].in_board,
                       players[player->last_victim->color].in_base);
            }
            break;
            
        case H_PATH:
            if (player->last_move) {
                printf("[%s] piece %c%d enters the home path at position %d.\n",
                       color_name,
                       get_color_name(color)[0],
                       player->last_move->id,
                       dest_pos);
            }
            break;
            
        case HOME:
            if (player->last_move) {
                printf("[%s] piece %c%d reaches home!\n",
                       color_name,
                       get_color_name(color)[0],
                       player->last_move->id);
                printf("[%s] player now has %d/4 pieces at home.\n",
                       color_name,
                       player->in_home);
            }
            break;
            
        case NON:
            printf("[%s] cannot move this turn.\n", color_name);
            break;
    }
    
    // After each round status
    printf("\n============================\n");
    printf("Location of pieces [%s]\n", color_name);
    printf("============================\n");
    
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece* p = &all_pieces[color][i];
        printf("Piece %c%d -> ", get_color_name(color)[0], p->id);
        
        if (p->is_in_base) {
            printf("Base\n");
        } else if (p->is_in_home) {
            printf("Home\n");
        } else if (p->is_in_home_path) {
            printf("Home path position %d\n", p->home_index);
        } else {
            printf("Board position %d\n", p->board_pos);
        }
    }
}




int can_move_piece(Piece *p, int die_roll, int* target_pos, int* is_capture, int* path_blocked,int* can_home_entry, int* block_capture , int* block_move, int* block_make) {
    *path_blocked = 0;
    *block_capture = 0;
    *block_move = 0;
    *can_home_entry = 0;
    *block_make = 0;
    
    // Safety check
    if (!p) {
        if (DEBUG) printf("ERROR: Null piece pointer\n");
        return 0;
    }
    
    if (p->is_in_base) {
        if (die_roll != 6) return 0; // can't come out
        int start = START_POSITION[p->color];
        Cell* cell = &board[start];

        // normal out

        if (cell->count == 0) {
            if (target_pos) *target_pos = start;
            if (is_capture) *is_capture = 0;
            return 1;
        }
        
        // capture if opponent
        if ((cell->count == 1) && (cell->occupants[0]->color != p->color)) {
            if (target_pos) *target_pos = start;
            if (is_capture) *is_capture = 1;
            return 1;
        }
        
        // Can stack with same color pieces
        if (cell->count >= 1 && cell->occupants[0]->color == p->color) {
            if (target_pos) *target_pos = start; 
            if (is_capture) *is_capture = 0;
            return 1;
        }

        return 0; // blocked by opponent
    }

    if (p->is_in_board) {
        // Safety check for board position
        if (p->board_pos < 0 || p->board_pos >= STD_BOARD_SIZE) {
            if (DEBUG) printf("ERROR: Invalid board position %d for piece %s%d\n", p->board_pos, get_color_name(p->color), p->id);
            return 0;
        }
        int new_pos;
        Direction direction = p->direction;

        Cell* curr_cell = &board[p->board_pos];
        // if not a block new position
        if (!(curr_cell->superblock)){
            if (direction == CLOCKWISE) {
                new_pos = (p->board_pos + die_roll) % STD_BOARD_SIZE;
            } else {
                new_pos = (p->board_pos - die_roll + STD_BOARD_SIZE) % STD_BOARD_SIZE;
            }
        } else if (curr_cell->superblock) {
            //  if a superblock make new pos according to logic
            if (curr_cell->block_direction == CLOCKWISE) {
                new_pos = (p->board_pos + (die_roll/(int)(curr_cell->count))) % STD_BOARD_SIZE;
            } else if (curr_cell->block_direction == ANTICLOCKWISE){
                new_pos = (p->board_pos - (die_roll/(int)(curr_cell->count)) + STD_BOARD_SIZE) % STD_BOARD_SIZE;
            } else {
                return 0; // error
            }
        } else {
            if (DEBUG) printf("Something wrong with the block logic; cell %i doe't have a superblock set", p->board_pos);
            return 0;
        }
        Cell* dest_cell = &board[new_pos];
        // CS-3: Enhanced blocking logic - Check if destination is blocked by opponents
        if (dest_cell->count >= 2 && dest_cell->occupants[0]->color != p->color) {
            if (is_capture) *is_capture = 0;
            // CS-8: Allow equal-size block capture only
            if (curr_cell->superblock && curr_cell->count == dest_cell->count) {
                // Equal size block capture is allowed
                if (is_capture) *is_capture = 1;
                if (block_capture) *block_capture = 1;
                if (block_move) *block_move = 1;
                if (target_pos) *target_pos = new_pos;
                return 1;
            }
            return 0; // blocked by opponent block
        }

        // check for home entry and blocks
        int home_entry = HOME_ENTRY_POSITION[p->color];
        if (DEBUG) printf("DEBUG: Piece %s%d at pos %d, die_roll %d, dist_from_home is %d, dist_from_start is %d, home_entry at %d direction is %s\n", get_color_name(p->color),
               p->id, p->board_pos, die_roll, p->remaining_to_home-die_roll, p->dist_from_start+die_roll, home_entry, get_direction(direction));
        
        // Check if the move would cross or land on the home entry position
        int crosses_home_entry = 0;
        int steps_into_home = 0;
        
        for (int i = 0; i <= die_roll; i++) {
            int check_pos = (direction == CLOCKWISE) ? (p->board_pos + i) % STD_BOARD_SIZE : (p->board_pos - i + STD_BOARD_SIZE) % STD_BOARD_SIZE ; 
            Cell* check_cell = &board[check_pos];
            
            // If there's a block of 2+ opponent pieces in the path
            if (check_cell->count >= 2 && check_cell->occupants[0]->color != p->color && !curr_cell->superblock) {
                *path_blocked = 1;
                // avoid negative array access
                if (i > 0) {
                    int prev_pos = (direction==CLOCKWISE) ? (p->board_pos + i - 1) % STD_BOARD_SIZE : (p->board_pos - i + STD_BOARD_SIZE + 1) % STD_BOARD_SIZE;
                    Cell *check_previous = &board[prev_pos];
                    if (check_previous->count < 2 || check_previous->occupants[0]->color == p->color) {
                        if (target_pos) *target_pos = prev_pos;
                        if (is_capture && check_previous->count > 0 && check_previous->occupants[0]->color != p->color) {
                            *is_capture = 1;
                        } else if (is_capture) {
                            *is_capture = 0;
                        }
                        return 1;
                    }
                }
                return 0;
            }
            if ( (p->remaining_to_home) - i <= 0) {
                crosses_home_entry = 1;
                steps_into_home = die_roll - i;
                if (DEBUG) printf("DEBUG: Crosses home entry at step %d, steps_into_home = %d\n", i, steps_into_home);
                break;
            }
        }
        
        // check can enter home path
        if (crosses_home_entry && steps_into_home > 0) {
            if (steps_into_home >= HOME_PATH) {
                if (DEBUG) printf("DEBUG: Would overshoot home path (steps_into_home=%d, HOME_PATH=%d)\n", 
                       steps_into_home, HOME_PATH);
                return 0; // Overshoot home path
            }
            
            if (DEBUG) printf("DEBUG: Entering home path at index %d\n", steps_into_home);
            if (target_pos) *target_pos = (steps_into_home - 1); 
            if (is_capture) *is_capture = 0;
            if (can_home_entry) *can_home_entry = 1;
            return 1;
        }

        // Can capture single opponent piece
        if (dest_cell->count == 1 && dest_cell->occupants[0]->color != p->color) {
            if (target_pos) *target_pos = new_pos;
            if (is_capture) *is_capture = 1;
            return 1;
        }
        // CS-3 & CS-4: Enhanced block formation and movement logic
         if (dest_cell->count >= 1) {
            // CS-3: Make a block with same color pieces
            if (dest_cell->occupants[0]->color == p->color) {
                if (target_pos) *target_pos = new_pos;
                if (is_capture) *is_capture = 0;
                if (block_make) *block_make = 1; // Fixed assignment
                return 1;
            }
            // CS-8: Capture equal-size superblock (already handled above)
            if (curr_cell->superblock && dest_cell->count == curr_cell->count && dest_cell->occupants[0]->color != p->color){
                if (DEBUG) printf("CS-8: Attempting equal-size block capture\n");
                if (is_capture) *is_capture = 1;
                if (target_pos) *target_pos = new_pos;
                if (block_capture) *block_capture = 1;
                if (block_move) *block_move = 1;
                return 1;
            }
            // CS-4: Move superblock to empty space
            if (curr_cell->superblock && dest_cell->count == 0) {
                if (is_capture) *is_capture = 0;
                if (target_pos) *target_pos = new_pos;
                if (block_move) *block_move = 1;
                return 1; // Added missing return
            }
         }

        // Can move to empty space 
        if (dest_cell->count == 0){
            if (target_pos) *target_pos = new_pos;
            if (is_capture) *is_capture = 0;
            return 1;
        }

        return 0;
    }

    if (p->is_in_home_path) {
        // Safety check home index
        if (p->home_index < 0 || p->home_index >= HOME_PATH) {
            if (DEBUG) printf("ERROR: Invalid home index %d for piece %s%d\n", p->home_index, get_color_name(p->color), p->id);
            return 0;
        }
        
        if (p->home_index + die_roll == HOME_PATH) {
            // reaching home (final position)
            if (DEBUG) printf("DEBUG: Reaching final home position\n");
            if (target_pos) *target_pos = HOME_PATH;
            if (is_capture) *is_capture = 0;
            return 1;
        }
        

    }
    return 0;

}

// 2. Fix in apply_move() - Array bounds and logic issues
void apply_move(Piece* p, int die_roll, int target_pos, int is_capture, int* did_capture, int can_home_entry, int block_capture, int block_move, int block_make) {
    *did_capture = 0;

    // Safety check
    if (!p) {
        if (DEBUG) printf("ERROR: Null piece pointer in apply_move\n");
        return;
    }

    // from base to start position
    if (p->is_in_base) {
        p->is_in_base = 0;
        p->is_in_board = 1;
        p->board_pos = target_pos;
        p->dist_from_start = 0;  // Just started
        //  Initialize remaining_to_home 
        p->remaining_to_home = STD_BOARD_SIZE - 1;
        int coin_flip = flip_coin();
        p->direction = coin_flip;
        p->original_direction = coin_flip; // CS-5: Track original direction when piece first enters board
        if (DEBUG) printf("Piece %s got direction %s\n", get_color_name(p->color), get_direction(coin_flip));
        // (START_POSITION[p->color] - HOME_ENTRY_POSITION[p->color] + STD_BOARD_SIZE) % STD_BOARD_SIZE + HOME_PATH;
        players[p->color].in_base--;
        players[p->color].in_board++;
        players[p->color].last_move = p;
        
        Cell* dest_cell = &board[target_pos];

        // CS-3 & CS-4: Enhanced block formation at start position
        if (dest_cell->count==1 && dest_cell->occupants[0]->direction != p->direction){
            dest_cell->superblock=1;
            // CS-4: Determine block direction based on piece farthest from home
            if (dest_cell->occupants[0]->remaining_to_home <= p->remaining_to_home) {
                dest_cell->block_direction = dest_cell->occupants[0]->direction;
            } else {
                dest_cell->block_direction = p->direction;
            }
            // CS-5: Store original directions for both pieces if not already stored
            if (dest_cell->occupants[0]->original_direction == UNKNOWN) {
                dest_cell->occupants[0]->original_direction = dest_cell->occupants[0]->direction;
            }
            if (p->original_direction == UNKNOWN) {
                p->original_direction = p->direction;
            }
        }

        // captures
        if (is_capture) {
            if (dest_cell->count > 0 && dest_cell->occupants[0]) {
                Piece* victim = dest_cell->occupants[0];
                // CS-9: Complete piece reset on capture
                victim->is_in_base = 1;
                victim->is_in_board = 0;
                victim->board_pos = -1;
                victim->dist_from_start = 0;
                victim->captures = 0;
                victim->direction = CLOCKWISE; // Reset to default
                victim->original_direction = UNKNOWN; // CS-5: Reset original direction
                victim->remaining_to_home = STD_BOARD_SIZE - 1; // Reset remaining distance
                victim->home_index = -1; // Reset home index
                victim->is_in_home_path = 0; // Ensure not in home path
                victim->is_in_home = 0; // Ensure not in home

                players[victim->color].in_base++;
                players[victim->color].in_board--;
                players[p->color].last_victim = victim;
                dest_cell->occupants[0] = p;
                dest_cell->count = 1;
                *did_capture = 1;
            }
        } else {
            dest_cell->occupants[dest_cell->count++] = p;
        }
        return;
    }

    // movement from board
    if (p->is_in_board) {
        // Safety check
        if (p->board_pos < 0 || p->board_pos >= STD_BOARD_SIZE) {
            if (DEBUG) printf("ERROR: Invalid board position %d in apply_move\n", p->board_pos);
            return;
        }
        Direction direction = p->direction;
        // Remove from old position
        Cell* old_cell = &board[p->board_pos];
        int new_pos = target_pos;
        //  if (!(old_cell->superblock)){
        //     if (direction == CLOCKWISE) {
        //         new_pos = (p->board_pos + die_roll) % STD_BOARD_SIZE;
        //     } else {
        //         new_pos = (p->board_pos - die_roll + STD_BOARD_SIZE) % STD_BOARD_SIZE;
        //     }
        // } else if (old_cell->superblock) {
        //     //  if a superblock make new pos according to logic
        //     if (old_cell->block_direction == CLOCKWISE) {
        //         new_pos = (p->board_pos + (die_roll/(int)(old_cell->count))) % STD_BOARD_SIZE;
        //     } else if (old_cell->block_direction == ANTICLOCKWISE){
        //         new_pos = (p->board_pos - (die_roll/(int)(old_cell->count)) + STD_BOARD_SIZE) % STD_BOARD_SIZE;
        //     } else {
        //         return 0; // error
        //     }
        // } else {
        //     if (DEBUG) printf("Something wrong with the block logic; cell %i doe't have a superblock set", p->board_pos);
        //     return 0;
        // }
        Cell* dest_cell = &board[new_pos];
        Piece* Other_moved = NULL;
        // CS-8 & CS-9: Enhanced capture logic for blocks
        if (is_capture && dest_cell->count > 0) {
            // CS-8: Capture all pieces in the destination block
            for (int i = 0; i < dest_cell->count; i++){
                if (dest_cell->occupants[i]) {
                    Piece* victim = dest_cell->occupants[i];
                    // CS-9: Complete piece reset on capture
                    victim->is_in_base = 1;
                    victim->is_in_board = 0;
                    victim->board_pos = -1;
                    victim->direction = CLOCKWISE; // Reset to default
                    victim->original_direction = UNKNOWN; // CS-5: Reset original direction
                    victim->captures = 0;
                    victim->dist_from_start = 0;
                    victim->remaining_to_home = STD_BOARD_SIZE - 1; // Reset remaining distance
                    victim->home_index = -1; // Reset home index
                    victim->is_in_home_path = 0; // Ensure not in home path
                    victim->is_in_home = 0; // Ensure not in home

                    players[victim->color].in_base++;
                    players[victim->color].in_board--;
                    players[p->color].last_victim = victim;
                }
            }
            // Clear destination cell and place capturing pieces
            dest_cell->count = 0;
            dest_cell->superblock = 0;
            dest_cell->block_direction = UNKNOWN;

            if (block_capture){
                // CS-8: All pieces in capturing block participate in capture
                for (int i=0; i <old_cell->count; i++){
                    if (old_cell->occupants[i]) {
                        old_cell->occupants[i]->captures++; // CS-8: Increment capture count for each piece
                        dest_cell->occupants[dest_cell->count++] = old_cell->occupants[i];
                        Other_moved = (p != old_cell->occupants[i]) ? old_cell->occupants[i]: NULL;
                    }
                }
                // Maintain block properties if moving as a block
                if (old_cell->superblock) {
                    dest_cell->superblock = 1;
                    dest_cell->block_direction = old_cell->block_direction;
                }
            } else {
                // Single piece capture
                dest_cell->occupants[dest_cell->count++] = p;
            }
            *did_capture = 1;
        } else {
            if (block_make) {
                // CS-3 & CS-4: Enhanced block formation
                dest_cell->superblock=1;
                int closest_to_home_idx = 0;
                // CS-4: Find piece farthest from home to determine block direction
                for (int i = 0; i < dest_cell->count; i++){
                    if (dest_cell->occupants[i] && dest_cell->occupants[i]->remaining_to_home < p->remaining_to_home){
                        closest_to_home_idx = i;
                    }
                }
                if (dest_cell->occupants[closest_to_home_idx]->remaining_to_home <= p->remaining_to_home) {
                    dest_cell->block_direction = dest_cell->occupants[closest_to_home_idx]->direction;
                } else {
                    dest_cell->block_direction = p->direction;
                }

                // CS-5: Store original directions for all pieces joining the block
                for (int i = 0; i < dest_cell->count; i++){
                    if (dest_cell->occupants[i] && dest_cell->occupants[i]->original_direction == UNKNOWN) {
                        dest_cell->occupants[i]->original_direction = dest_cell->occupants[i]->direction;
                    }
                }
                if (p->original_direction == UNKNOWN) {
                    p->original_direction = p->direction;
                }

                dest_cell->occupants[dest_cell->count++] = p;

            } else if (block_move) {
                // CS-4: Move entire block together
                for (int i =0; i < old_cell->count; i++){
                    if (old_cell->occupants[i]){
                        dest_cell->occupants[dest_cell->count++] = old_cell->occupants[i];
                        Other_moved = (p != old_cell->occupants[i]) ? old_cell->occupants[i]: NULL;
                    }
                }
                // Maintain block properties
                if (old_cell->superblock) {
                    dest_cell->superblock = 1;
                    dest_cell->block_direction = old_cell->block_direction;
                }
            } else {
                dest_cell->occupants[dest_cell->count++] = p;
            }
        }


        // CS-5: Remove from old position and handle direction restoration
        for (int i = 0; i < old_cell->count; i++) {
            if (old_cell->occupants[i] == p || (Other_moved && old_cell->occupants[i] == Other_moved)) {
                // CS-5: Restore original direction when piece leaves a block
                if (old_cell->superblock && old_cell->occupants[i]->original_direction != UNKNOWN) {
                    old_cell->occupants[i]->direction = old_cell->occupants[i]->original_direction;
                    if (DEBUG) printf("CS-5: Restored piece %s%d direction to %s\n",
                                    get_color_name(old_cell->occupants[i]->color),
                                    old_cell->occupants[i]->id,
                                    get_direction(old_cell->occupants[i]->direction));
                }

                for (int j = i; j < old_cell->count - 1; j++) {
                    old_cell->occupants[j] = old_cell->occupants[j + 1];
                }
                old_cell->count--;

                // If this was the last piece in a block, clear block properties
                if (old_cell->count <= 1) {
                    old_cell->superblock = 0;
                    old_cell->block_direction = UNKNOWN;
                    // CS-5: Restore direction for remaining piece if any
                    if (old_cell->count == 1 && old_cell->occupants[0]->original_direction != UNKNOWN) {
                        old_cell->occupants[0]->direction = old_cell->occupants[0]->original_direction;
                        if (DEBUG) printf("CS-5: Restored remaining piece %s%d direction to %s\n",
                                        get_color_name(old_cell->occupants[0]->color),
                                        old_cell->occupants[0]->id,
                                        get_direction(old_cell->occupants[0]->direction));
                    }
                }
                break;
            }
        }

        if (can_home_entry && target_pos >= 0) {
            if (DEBUG) printf("DEBUG: Applying move to home path, home_index = %d\n", target_pos);
            
            // Safety check
            if (target_pos <= 0 || target_pos > HOME_PATH-1) {
                if (DEBUG) printf("ERROR: Invalid home index %d when entering home path\n", target_pos);
                return;
            }
            
            p->is_in_board = 0;
            p->is_in_home_path = 1;
            p->board_pos = -1;
            p->home_index = target_pos;
            p->remaining_to_home = HOME_PATH - (target_pos+1);
            p->direction = CLOCKWISE;
            players[p->color].last_move = p;
            players[p->color].in_board--;
            return;
        }

        // Regular board movement



        p->board_pos = new_pos;
        if (Other_moved) {
            Other_moved->board_pos=new_pos;
            Other_moved->dist_from_start = Other_moved->dist_from_start + die_roll;
            Other_moved->remaining_to_home = Other_moved->remaining_to_home - die_roll;
        }
        players[p->color].last_move = p;
        p->dist_from_start = p->dist_from_start + die_roll;
        p->remaining_to_home = p->remaining_to_home - die_roll;
        return;
    }

    // movement within home path
    if (p->is_in_home_path) {
        if (target_pos == HOME_PATH) {
            // Piece has reached home
            if (DEBUG) printf("DEBUG: Piece %s%d reaches final home\n", get_color_name(p->color), p->id);
            p->home_index = -1;
            p->is_in_home = 1;
            p->remaining_to_home = 0;
            p->is_in_home_path = 0;
            p->board_pos = -1;
            p->is_in_board = 0;
            p->is_in_base = 0;
            p->dist_from_start = STD_BOARD_SIZE;

            players[p->color].last_move = p;
            players[p->color].in_home++;
            return;
        }
        

    }
}

int play_red_turn(int die_roll) {
    Piece* best_piece = NULL;
    int pieces_on_board = 0;
    int highest_priority = -1;
    int best_target = -1;
    int best_capture = 0;
    int best_path_blocked = 0;
    int home_entry = 0;
    Outcomes outcome; // for print

    // Count pieces on board
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        if (all_pieces[RED][i].is_in_board) pieces_on_board++;
    }

    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece *p = &all_pieces[RED][i];
        int target, is_capture, path_blocked, block_capture, can_home_entry, block_move, block_make;
        if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked, &can_home_entry, &block_capture, &block_move, &block_make)) {
            int priority = 0;
            Outcomes _outcome;
            // Highest priority: Captures
            if (is_capture) {
                priority = 1000 + p->dist_from_start;
                _outcome = CAPTURE;
            }
            // Second priority: Moving pieces in home path
            else if (p->is_in_home_path) {
                priority = 500 + p->home_index;
                if (target == HOME_PATH) {
                    _outcome = HOME;
                } else {
                    _outcome = H_PATH;
                }
            }
            // Third priority: Getting pieces out of base
            else if (p->is_in_base && die_roll == 6 && pieces_on_board < 2) {
                priority = 300;
                _outcome = (is_capture) ? FR_BASE+CAPTURE: FR_BASE;;
            }
            // Fourth priority: Moving pieces on board
            else if (p->is_in_board) {
                priority = 100 + p->dist_from_start;

                // avoid blocks
                Cell *dest = &board[target];
                if (dest->count >= 1 && dest->occupants[0]->color == RED) {
                    priority -= 60;
                }
                _outcome = ST_PATH;
            }
            // Last priority: Getting pieces out of base
            // else if (p->is_in_base && die_roll == 6) {
            //     priority = 50;
            //     _outcome = (is_capture) ? FR_BASE+CAPTURE: FR_BASE;
            // }

            if (priority > highest_priority) {
                best_piece = p;
                best_target = target;
                best_capture = is_capture;
                best_path_blocked = path_blocked;
                highest_priority = priority;
                outcome = _outcome;
                home_entry = can_home_entry;
            }
        }
    }
    
    if (!best_piece) {
        printf("RED cannot move this turn.\n");
        outcome = NON;
        return 0;
    }
    
    int did_capture = 0;
    
    // Save initial state for display
    int was_in_base = best_piece->is_in_base;
    int was_in_board = best_piece->is_in_board;
    int was_in_home_path = best_piece->is_in_home_path;
    int old_pos = best_piece->board_pos;
    int old_home_index = best_piece->home_index;

    // Get the actual block parameters for the chosen move
    int target, is_capture, path_blocked, block_capture, can_home_entry, block_move, block_make;
    can_move_piece(best_piece, die_roll, &target, &is_capture, &path_blocked, &can_home_entry, &block_capture, &block_move, &block_make);

    apply_move(best_piece, die_roll, best_target, best_capture, &did_capture, home_entry, block_capture, block_move, block_make);

    // Determine source position for display
    int src_pos;
    if (was_in_base) {
        src_pos = 0;  // base
    } else if (was_in_board) {
        src_pos = old_pos;
    } else if (was_in_home_path) {
        src_pos = old_home_index;
    } else {
        src_pos = -1;  // unknown
    }

    // Calculate original target position
    int original_target = -1;
    if (was_in_base && die_roll == 6) {
        original_target = START_POSITION[RED];
    } else if (was_in_board) {
        original_target = (old_pos + die_roll) % STD_BOARD_SIZE;
    } else if (was_in_home_path) {
        original_target = old_home_index + die_roll;
    }

    print_outcome(outcome, &players[RED], src_pos, best_target, (did_capture) ? players[RED].last_victim : NULL, (best_target >= 0 && best_target < STD_BOARD_SIZE) ? &board[best_target] : NULL, original_target, best_path_blocked, die_roll);

    if (DEBUG) printf("RED moves piece R%d from %d to %d%s\n",
        best_piece->id,
        src_pos,
        best_target,
        did_capture ? " and gets a bonus roll!" : "");

    return did_capture;
}

int play_green_turn(int die_roll) {
    Piece* best_piece = NULL;
    int pieces_on_board = 0;
    int highest_priority = -1;
    int best_target = -1;
    int captured = 0; //if capture or not state for the apply move function
    int best_path_blocked = 0;
    int home_entry = 0;
    Outcomes outcome; // for print


    // Count pieces on board
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        if (all_pieces[GREEN][i].is_in_board) pieces_on_board++;
    }

    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece *p = &all_pieces[GREEN][i];
        int target, is_capture, path_blocked, block_capture, can_home_entry, block_move, block_make;

        if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked, &can_home_entry, &block_capture, &block_move, &block_make)) {
            int priority = 0;
            Outcomes _outcome;
            // Highest priority: Block
            Cell *target_cell = &board[target];
            if (target_cell->count > 0 && target_cell->occupants[0]->color==p->color && target != START_POSITION[GREEN]){
                priority= 1000 + p->dist_from_start;
                _outcome = ST_PATH;
            }

            // High priority: Getting out from base
            else if (p->is_in_base && die_roll == 6) {
                priority = 500;
                _outcome = (is_capture) ? CAPTURE+FR_BASE : FR_BASE;
            }

            // if (is_capture) {
            //     priority = 1000 + p->dist_from_start;
            // }
            // Second priority: Moving pieces in home path

            else if (p->is_in_home_path) {
                priority = 300 + p->home_index;
                if (target == HOME_PATH) {
                    _outcome = HOME;
                } else {
                    _outcome = H_PATH;
                }
            }

            // Fourth priority: Moving pieces on board
            else if (p->is_in_board) {
                priority = 100 + p->dist_from_start;

                // avoid blocks
                Cell *current = &board[p->board_pos];
                if (current->count >= 1 && current->occupants[0]->color == p->color) {
                    priority -= 90;
                }
                _outcome = (is_capture) ? CAPTURE : ST_PATH;
            }
            // Last priority: capture
            else if (is_capture) {
                priority = 50;
                _outcome = CAPTURE;
            }

            if (priority > highest_priority) {
                best_piece = p;
                best_target = target;
                captured = is_capture;
                best_path_blocked = path_blocked;
                highest_priority = priority;
                outcome = _outcome;
                home_entry = can_home_entry;
            }
        }
    }

    if (!best_piece) {
        printf("GREEN cannot move this turn.\n");
        outcome = NON;
        return 0;
    }
    
    int did_capture = 0;
    
    // Save initial state for display
    int was_in_base = best_piece->is_in_base;
    int was_in_board = best_piece->is_in_board;
    int was_in_home_path = best_piece->is_in_home_path;
    int old_pos = best_piece->board_pos;
    int old_home_index = best_piece->home_index;

    // Get the actual block parameters for the chosen move
    int target, is_capture, path_blocked, block_capture, can_home_entry, block_move, block_make;
    can_move_piece(best_piece, die_roll, &target, &is_capture, &path_blocked, &can_home_entry, &block_capture, &block_move, &block_make);

    apply_move(best_piece, die_roll, best_target, captured, &did_capture, home_entry, block_capture, block_move, block_make);

    // Determine source position for display
    int src_pos;
    if (was_in_base) {
        src_pos = 0;  // base
    } else if (was_in_board) {
        src_pos = old_pos;
    } else if (was_in_home_path) {
        src_pos = old_home_index;
    } else {
        src_pos = -1;  // unknown
    }

    // Calculate original target position
    int original_target = -1;
    if (was_in_base && die_roll == 6) {
        original_target = START_POSITION[GREEN];
    } else if (was_in_board) {
        original_target = (old_pos + die_roll) % STD_BOARD_SIZE;
    } else if (was_in_home_path) {
        original_target = old_home_index + die_roll;
    }

    print_outcome(outcome, &players[GREEN], src_pos, best_target, (did_capture) ? players[GREEN].last_victim : NULL, (best_target >= 0 && best_target < STD_BOARD_SIZE) ? &board[best_target] : NULL, original_target, best_path_blocked, die_roll);

    if (DEBUG) printf("GREEN moves piece G%d from %d to %d%s\n",
        best_piece->id,
        src_pos,
        best_target,
        did_capture ? " and gets a bonus roll!" : "");

    return did_capture;
}


int play_yellow_turn(int die_roll) {
    Piece* best_piece = NULL;
    int pieces_on_board = 0;
    int highest_priority = -1;
    int best_target = -1;
    int captured = 0; //if capture or not state for the apply move function
    int best_path_blocked = 0;
    int home_entry = 0;
    Outcomes outcome; // for print


    // Count pieces on board
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        if (all_pieces[YELLOW][i].is_in_board) pieces_on_board++;
    }

    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece *p = &all_pieces[YELLOW][i];
        int target, is_capture, path_blocked, block_capture, can_home_entry, block_move, block_make;

        if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked, &can_home_entry, &block_capture, &block_move, &block_make)) {
            int priority = 0;
            Outcomes _outcome;

            // Highest priority: Getting out from base
            if (p->is_in_base && die_roll == 6) {
                priority = 1000;
                _outcome = (is_capture) ? CAPTURE+FR_BASE: FR_BASE;
            }
            else if (p->is_in_home_path) {
                priority = 500 + p->home_index;
                if (target == HOME_PATH) {
                    _outcome = HOME;
                } else {
                    _outcome = H_PATH;
                }
            }
            // High priority: capture
            else if (is_capture) {
                priority = 300 + p->dist_from_start;
                _outcome = CAPTURE;
            }

            // Next priority: Moving pieces on board
            else if (p->is_in_board) {
                priority = 100 + p->dist_from_start;

                // avoid blocks
                Cell *current = &board[p->board_pos];
                if (current->count >= 1 && current->occupants[0]->color == p->color) {
                    priority -= 10;
                }
                _outcome = ST_PATH;
            }

            if (priority > highest_priority) {
                best_piece = p;
                best_target = target;
                captured = is_capture;
                best_path_blocked = path_blocked;
                highest_priority = priority;
                outcome = _outcome;
                home_entry=can_home_entry;
            }
        }
    }

    if (!best_piece) {
        printf("YELLOW cannot move this turn.\n");
        outcome = NON;
        return 0;
    }
    
    int did_capture = 0;
    
    // Save initial state for display
    int was_in_base = best_piece->is_in_base;
    int was_in_board = best_piece->is_in_board;
    int was_in_home_path = best_piece->is_in_home_path;
    int old_pos = best_piece->board_pos;
    int old_home_index = best_piece->home_index;

    // Get the actual block parameters for the chosen move
    int target, is_capture, path_blocked, block_capture, can_home_entry, block_move, block_make;
    can_move_piece(best_piece, die_roll, &target, &is_capture, &path_blocked, &can_home_entry, &block_capture, &block_move, &block_make);

    apply_move(best_piece, die_roll, best_target, captured, &did_capture, home_entry, block_capture, block_move, block_make);

    // Determine source position for display
    int src_pos;
    if (was_in_base) {
        src_pos = 0;  // base
    } else if (was_in_board) {
        src_pos = old_pos;
    } else if (was_in_home_path) {
        src_pos = old_home_index;
    } else {
        src_pos = -1;  // unknown
    }

    // Calculate original target position
    int original_target = -1;
    if (was_in_base && die_roll == 6) {
        original_target = START_POSITION[YELLOW];
    } else if (was_in_board) {
        original_target = (old_pos + die_roll) % STD_BOARD_SIZE;
    } else if (was_in_home_path) {
        original_target = old_home_index + die_roll;
    }

    print_outcome(outcome, &players[YELLOW], src_pos, best_target, (did_capture) ? players[YELLOW].last_victim : NULL, (best_target >= 0 && best_target < STD_BOARD_SIZE) ? &board[best_target] : NULL, original_target, best_path_blocked, die_roll);

    if (DEBUG) printf("YELLOW moves piece Y%d from %d to %d%s\n",
        best_piece->id,
        src_pos,
        best_target,
        did_capture ? " and gets a bonus roll!" : "");

    return did_capture;
}




int play_blue_turn(int die_roll) {
    Piece* best_piece = NULL;
    int pieces_on_board = 0;
    int highest_priority = -1;
    int best_target = -1;
    int captured = 0; //if capture or not state for the apply move function
    int best_path_blocked = 0;
    int home_entry = 0;
    Outcomes outcome; // for print


    // Count pieces on board
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        if (all_pieces[BLUE][i].is_in_board) pieces_on_board++;
    }

    int last_piece_id = (players[BLUE].last_move)? (players[BLUE].last_move->id) : 4; // 4 because 4%4=0

    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece *p = &all_pieces[BLUE][(last_piece_id+i)%PIECES_PER_PLAYER];
        int target, is_capture, path_blocked, block_capture, can_home_entry, block_move, block_make;

        if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked, &can_home_entry, &block_capture, &block_move, &block_make)) {
            int priority = 0;
            Outcomes _outcome;

            // Determine outcome based on move type
            if (p->is_in_base && die_roll == 6) {
                _outcome = (is_capture) ? FR_BASE+CAPTURE : FR_BASE;
            } else if (p->is_in_home_path) {
                if (target == HOME_PATH) {
                    _outcome = HOME;
                } else {
                    _outcome = H_PATH;
                }
            } else if (is_capture) {
                _outcome = CAPTURE;
            } else if (p->is_in_board) {
                _outcome = ST_PATH;
            } else {
                _outcome = ST_PATH; // default
            }

            best_piece = p;
            best_target = target;
            captured = is_capture;
            best_path_blocked = path_blocked;
            outcome = _outcome;
            home_entry=can_home_entry;
            break;
            // no priority order yet

        }
    }

    if (!best_piece) {
        printf("BLUE cannot move this turn.\n");
        outcome = NON;
        return 0;
    }
    
    int did_capture;
    
    // Save initial state for display
    int was_in_base = best_piece->is_in_base;
    int was_in_board = best_piece->is_in_board;
    int was_in_home_path = best_piece->is_in_home_path;
    int old_pos = best_piece->board_pos;
    int old_home_index = best_piece->home_index;

    // Get the actual block parameters for the chosen move
    int target, is_capture, path_blocked, block_capture, can_home_entry, block_move, block_make;
    can_move_piece(best_piece, die_roll, &target, &is_capture, &path_blocked, &can_home_entry, &block_capture, &block_move, &block_make);

    apply_move(best_piece, die_roll, best_target, captured, &did_capture, home_entry, block_capture, block_move, block_make);

    // Determine source position for display
    int src_pos;
    if (was_in_base) {
        src_pos = 0;  // base
    } else if (was_in_board) {
        src_pos = old_pos;
    } else if (was_in_home_path) {
        src_pos = old_home_index;
    } else {
        src_pos = -1;  // unknown
    }

    // Calculate original target position
    int original_target = -1;
    if (was_in_base && die_roll == 6) {
        original_target = START_POSITION[BLUE];
    } else if (was_in_board) {
        original_target = (old_pos + die_roll) % STD_BOARD_SIZE;
    } else if (was_in_home_path) {
        original_target = old_home_index + die_roll;
    }

    print_outcome(outcome, &players[BLUE], src_pos, best_target, (did_capture) ? players[BLUE].last_victim : NULL, (best_target >= 0 && best_target < STD_BOARD_SIZE) ? &board[best_target] : NULL, original_target, best_path_blocked, die_roll);

    if (DEBUG) printf("BLUE moves piece B%d from %d to %d%s\n",
        best_piece->id,
        src_pos,
        best_target,
        did_capture ? " and gets a bonus roll!" : "");

    return did_capture;
}
