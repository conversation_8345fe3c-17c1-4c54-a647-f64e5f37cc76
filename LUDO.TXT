Yellow player rolls 5
Blue player rolls 1
Red player rolls 2
Green player rolls 6
Green player has the highest roll and will begin the game.

The order of a single round is <PERSON>, <PERSON>, <PERSON>, <PERSON>



Green rolls 5
GREEN cannot move this turn.


Yellow rolls 2
YELLOW cannot move this turn.


Blue rolls 5
BLUE cannot move this turn.


Red rolls 1
RED cannot move this turn.


<PERSON> rolls 5
GREEN cannot move this turn.


Yellow rolls 4
YELLOW cannot move this turn.


Blue rolls 1
BLUE cannot move this turn.


Red rolls 3
RED cannot move this turn.


Green rolls 6
Piece Green got direction Clockwise
[Green] player moves piece G1 to the starting point.
[Green] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Base
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 0 to 39


Yellow rolls 3
YELLOW cannot move this turn.


Blue rolls 1
BLUE cannot move this turn.


Red rolls 3
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[<PERSON>] moves piece G1 from location L39 to L41 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 41
Piece G2 -> Base
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 39 to 41


Yellow rolls 3
YELLOW cannot move this turn.


Blue rolls 5
BLUE cannot move this turn.


Red rolls 5
RED cannot move this turn.


Green rolls 1
DEBUG: Piece Green1 at pos 41, die_roll 1, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L41 to L42 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 42
Piece G2 -> Base
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 41 to 42


Yellow rolls 4
YELLOW cannot move this turn.


Blue rolls 2
BLUE cannot move this turn.


Red rolls 2
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 42, die_roll 2, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L42 to L44 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 44
Piece G2 -> Base
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 42 to 44


Yellow rolls 5
YELLOW cannot move this turn.


Blue rolls 3
BLUE cannot move this turn.


Red rolls 2
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green1 at pos 44, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Clockwise
Piece Green got direction Counter-Clockwise
[Green] player moves piece G2 to the starting point.
[Green] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 44
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G2 from 0 to 39


Yellow rolls 3
YELLOW cannot move this turn.


Blue rolls 3
BLUE cannot move this turn.


Red rolls 1
RED cannot move this turn.


Green rolls 5
DEBUG: Piece Green1 at pos 44, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L44 to L49 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 49
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 44 to 49


Yellow rolls 1
YELLOW cannot move this turn.


Blue rolls 6
Piece Blue got direction Counter-Clockwise
[Blue] player moves piece B1 to the starting point.
[Blue] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Base
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B1 from 0 to 13


Red rolls 1
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 49, die_roll 2, dist_from_home is 39, dist_from_start is 12, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L49 to L51 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 51
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 49 to 51


Yellow rolls 3
YELLOW cannot move this turn.


Blue rolls 5
DEBUG: Piece Blue1 at pos 13, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L13 to L8 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 8
Piece B2 -> Base
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B1 from 13 to 8


Red rolls 5
RED cannot move this turn.


Green rolls 4
DEBUG: Piece Green1 at pos 51, die_roll 4, dist_from_home is 35, dist_from_start is 16, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L51 to L3 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 3
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 51 to 3


Yellow rolls 4
YELLOW cannot move this turn.


Blue rolls 6
Piece Blue got direction Clockwise
[Blue] player moves piece B2 to the starting point.
[Blue] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 8
Piece B2 -> Board position 13
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B2 from 0 to 13


Red rolls 4
RED cannot move this turn.


Green rolls 5
DEBUG: Piece Green1 at pos 3, die_roll 5, dist_from_home is 30, dist_from_start is 21, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Counter-Clockwise
CAPTURE: [Green] piece G1 lands on square 8, captures [Blue] piece B1, and returns it to the base.
[Blue] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 8
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 3 to 8 and gets a bonus roll!
Player Green gets a bonus roll

Green rolls 2
DEBUG: Piece Green1 at pos 8, die_roll 2, dist_from_home is 28, dist_from_start is 23, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L8 to L10 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 10
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 8 to 10


Yellow rolls 3
YELLOW cannot move this turn.


Blue rolls 4
DEBUG: Piece Blue2 at pos 13, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L13 to L17 by 4 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 17
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B2 from 13 to 17


Red rolls 3
RED cannot move this turn.


Green rolls 1
DEBUG: Piece Green1 at pos 10, die_roll 1, dist_from_home is 27, dist_from_start is 24, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L10 to L11 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 11
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 10 to 11


Yellow rolls 5
YELLOW cannot move this turn.


Blue rolls 3
DEBUG: Piece Blue2 at pos 17, die_roll 3, dist_from_home is 44, dist_from_start is 7, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L17 to L20 by 3 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 20
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B2 from 17 to 20


Red rolls 5
RED cannot move this turn.


Green rolls 5
DEBUG: Piece Green1 at pos 11, die_roll 5, dist_from_home is 22, dist_from_start is 29, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L11 to L16 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 16
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 11 to 16


Yellow rolls 5
YELLOW cannot move this turn.


Blue rolls 6
Piece Blue got direction Counter-Clockwise
[Blue] player moves piece B3 to the starting point.
[Blue] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 20
Piece B3 -> Board position 13
Piece B4 -> Base
BLUE moves piece B3 from 0 to 13


Red rolls 1
RED cannot move this turn.


Green rolls 5
DEBUG: Piece Green1 at pos 16, die_roll 5, dist_from_home is 17, dist_from_start is 34, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L16 to L21 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 21
Piece G2 -> Board position 39
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 16 to 21


Yellow rolls 6
Piece Yellow got direction Counter-Clockwise
[Yellow] player moves piece Y1 to the starting point.
[Yellow] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 0
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 0 to 0


Blue rolls 1
DEBUG: Piece Blue2 at pos 20, die_roll 1, dist_from_home is 43, dist_from_start is 8, home_entry at 11 direction is Clockwise
CAPTURE: [Blue] piece B2 lands on square 21, captures [Green] piece G1, and returns it to the base.
[Green] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 21
Piece B3 -> Board position 13
Piece B4 -> Base
BLUE moves piece B2 from 20 to 21 and gets a bonus roll!
Player Blue gets a bonus roll

Blue rolls 3
DEBUG: Piece Blue3 at pos 13, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L13 to L10 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 21
Piece B3 -> Board position 10
Piece B4 -> Base
BLUE moves piece B3 from 13 to 10


Red rolls 5
RED cannot move this turn.


Green rolls 1
DEBUG: Piece Green2 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G2 from location L39 to L38 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Base
Piece G2 -> Board position 38
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G2 from 39 to 38


Yellow rolls 5
DEBUG: Piece Yellow1 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y1 from location L0 to L47 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 47
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 0 to 47


Blue rolls 3
DEBUG: Piece Blue2 at pos 21, die_roll 3, dist_from_home is 40, dist_from_start is 11, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L21 to L24 by 3 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 24
Piece B3 -> Board position 10
Piece B4 -> Base
BLUE moves piece B2 from 21 to 24


Red rolls 6
Piece Red got direction Clockwise
[Red] player moves piece R1 to the starting point.
[Red] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 26
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 0 to 26


Green rolls 2
DEBUG: Piece Green2 at pos 38, die_roll 2, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G2 from location L38 to L36 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Base
Piece G2 -> Board position 36
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G2 from 38 to 36


Yellow rolls 2
DEBUG: Piece Yellow1 at pos 47, die_roll 2, dist_from_home is 44, dist_from_start is 7, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y1 from location L47 to L45 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 45
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 47 to 45


Blue rolls 2
DEBUG: Piece Blue3 at pos 10, die_roll 2, dist_from_home is 46, dist_from_start is 5, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L10 to L8 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 24
Piece B3 -> Board position 8
Piece B4 -> Base
BLUE moves piece B3 from 10 to 8


Red rolls 3
DEBUG: Piece Red1 at pos 26, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L26 to L29 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 29
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 26 to 29


Green rolls 4
DEBUG: Piece Green2 at pos 36, die_roll 4, dist_from_home is 44, dist_from_start is 7, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G2 from location L36 to L32 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Base
Piece G2 -> Board position 32
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G2 from 36 to 32


Yellow rolls 5
DEBUG: Piece Yellow1 at pos 45, die_roll 5, dist_from_home is 39, dist_from_start is 12, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y1 from location L45 to L40 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 40
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 45 to 40


Blue rolls 5
DEBUG: Piece Blue2 at pos 24, die_roll 5, dist_from_home is 35, dist_from_start is 16, home_entry at 11 direction is Clockwise
CAPTURE: [Blue] piece B2 lands on square 29, captures [Red] piece R1, and returns it to the base.
[Red] player now has 0/4 pieces on the board and 4/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 29
Piece B3 -> Board position 8
Piece B4 -> Base
BLUE moves piece B2 from 24 to 29 and gets a bonus roll!
Player Blue gets a bonus roll

Blue rolls 5
DEBUG: Piece Blue3 at pos 8, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L8 to L3 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 29
Piece B3 -> Board position 3
Piece B4 -> Base
BLUE moves piece B3 from 8 to 3


Red rolls 3
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green2 at pos 32, die_roll 6, dist_from_home is 38, dist_from_start is 13, home_entry at 37 direction is Counter-Clockwise
Piece Green got direction Clockwise
[Green] player moves piece G1 to the starting point.
[Green] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 32
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G1 from 0 to 39


Yellow rolls 3
DEBUG: Piece Yellow1 at pos 40, die_roll 3, dist_from_home is 36, dist_from_start is 15, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y1 from location L40 to L37 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 37
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 40 to 37


Blue rolls 6
Piece Blue got direction Counter-Clockwise
[Blue] player moves piece B4 to the starting point.
[Blue] player now has 3/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 29
Piece B3 -> Board position 3
Piece B4 -> Board position 13
BLUE moves piece B4 from 0 to 13


Red rolls 1
RED cannot move this turn.


Green rolls 4
DEBUG: Piece Green1 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 32, die_roll 4, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G2 from location L32 to L28 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 28
Piece G3 -> Base
Piece G4 -> Base
GREEN moves piece G2 from 32 to 28


Yellow rolls 3
DEBUG: Piece Yellow1 at pos 37, die_roll 3, dist_from_home is 33, dist_from_start is 18, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y1 from location L37 to L34 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 34
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 37 to 34


Blue rolls 5
DEBUG: Piece Blue2 at pos 29, die_roll 5, dist_from_home is 30, dist_from_start is 21, home_entry at 11 direction is Clockwise
CAPTURE: [Blue] piece B2 lands on square 34, captures [Yellow] piece Y1, and returns it to the base.
[Yellow] player now has 0/4 pieces on the board and 4/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 3
Piece B4 -> Board position 13
BLUE moves piece B2 from 29 to 34 and gets a bonus roll!
Player Blue gets a bonus roll

Blue rolls 3
DEBUG: Piece Blue3 at pos 3, die_roll 3, dist_from_home is 38, dist_from_start is 13, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L3 to L0 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 0
Piece B4 -> Board position 13
BLUE moves piece B3 from 3 to 0


Red rolls 3
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green1 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 28, die_roll 6, dist_from_home is 34, dist_from_start is 17, home_entry at 37 direction is Counter-Clockwise
Piece Green got direction Clockwise
[Green] player moves piece G3 to the starting point.
[Green] player now has 3/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 28
Piece G3 -> Board position 39
Piece G4 -> Base
GREEN moves piece G3 from 0 to 39


Yellow rolls 5
YELLOW cannot move this turn.


Blue rolls 6
DEBUG: Piece Blue4 at pos 13, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L13 to L7 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 0
Piece B4 -> Board position 7
BLUE moves piece B4 from 13 to 7


Red rolls 2
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green1 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 28, die_roll 6, dist_from_home is 34, dist_from_start is 17, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
Piece Green got direction Clockwise
[Green] player moves piece G4 to the starting point.
[Green] player now has 4/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 28
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G4 from 0 to 39


Yellow rolls 1
YELLOW cannot move this turn.


Blue rolls 5
DEBUG: Piece Blue3 at pos 0, die_roll 5, dist_from_home is 33, dist_from_start is 18, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L0 to L47 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 47
Piece B4 -> Board position 7
BLUE moves piece B3 from 0 to 47


Red rolls 4
RED cannot move this turn.


Green rolls 3
DEBUG: Piece Green1 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 28, die_roll 3, dist_from_home is 37, dist_from_start is 14, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L28 to L25 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 25
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 28 to 25


Yellow rolls 4
YELLOW cannot move this turn.


Blue rolls 3
DEBUG: Piece Blue4 at pos 7, die_roll 3, dist_from_home is 42, dist_from_start is 9, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L7 to L4 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 47
Piece B4 -> Board position 4
BLUE moves piece B4 from 7 to 4


Red rolls 4
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green1 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 25, die_roll 6, dist_from_home is 31, dist_from_start is 20, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L25 to L19 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 19
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 25 to 19


Yellow rolls 2
YELLOW cannot move this turn.


Blue rolls 5
DEBUG: Piece Blue3 at pos 47, die_roll 5, dist_from_home is 28, dist_from_start is 23, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L47 to L42 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 42
Piece B4 -> Board position 4
BLUE moves piece B3 from 47 to 42


Red rolls 5
RED cannot move this turn.


Green rolls 3
DEBUG: Piece Green1 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 19, die_roll 3, dist_from_home is 28, dist_from_start is 23, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L19 to L16 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 16
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 19 to 16


Yellow rolls 5
YELLOW cannot move this turn.


Blue rolls 5
DEBUG: Piece Blue4 at pos 4, die_roll 5, dist_from_home is 37, dist_from_start is 14, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L4 to L51 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 42
Piece B4 -> Board position 51
BLUE moves piece B4 from 4 to 51


Red rolls 2
RED cannot move this turn.


Green rolls 1
DEBUG: Piece Green1 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 16, die_roll 1, dist_from_home is 27, dist_from_start is 24, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L16 to L15 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 15
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 16 to 15


Yellow rolls 6
Piece Yellow got direction Counter-Clockwise
[Yellow] player moves piece Y1 to the starting point.
[Yellow] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 0
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 0 to 0


Blue rolls 5
DEBUG: Piece Blue3 at pos 42, die_roll 5, dist_from_home is 23, dist_from_start is 28, home_entry at 11 direction is Counter-Clockwise
BLOCK: [Blue] piece B3 was blocked - moved from L42 to L40 instead of intended L47 (blocked by opponent pieces).

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 37
Piece B4 -> Board position 51
BLUE moves piece B3 from 42 to 40


Red rolls 1
RED cannot move this turn.


Green rolls 4
DEBUG: Piece Green1 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 15, die_roll 4, dist_from_home is 23, dist_from_start is 28, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L15 to L11 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 11
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 15 to 11


Yellow rolls 4
DEBUG: Piece Yellow1 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y1 from location L0 to L48 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 48
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 0 to 48


Blue rolls 6
DEBUG: Piece Blue4 at pos 51, die_roll 6, dist_from_home is 31, dist_from_start is 20, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L51 to L45 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 34
Piece B3 -> Board position 37
Piece B4 -> Board position 45
BLUE moves piece B4 from 51 to 45


Red rolls 1
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 11, die_roll 2, dist_from_home is 21, dist_from_start is 30, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L11 to L9 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 9
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 11 to 9


Yellow rolls 4
DEBUG: Piece Yellow1 at pos 48, die_roll 4, dist_from_home is 43, dist_from_start is 8, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y1 from location L48 to L44 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 44
Piece Y2 -> Base
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 48 to 44


Blue rolls 2
DEBUG: Piece Blue2 at pos 34, die_roll 2, dist_from_home is 28, dist_from_start is 23, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L34 to L36 by 2 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 36
Piece B3 -> Board position 37
Piece B4 -> Board position 45
BLUE moves piece B2 from 34 to 36


Red rolls 5
RED cannot move this turn.


Green rolls 1
DEBUG: Piece Green1 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 9, die_roll 1, dist_from_home is 20, dist_from_start is 31, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L9 to L8 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 8
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 9 to 8


Yellow rolls 6
DEBUG: Piece Yellow1 at pos 44, die_roll 6, dist_from_home is 37, dist_from_start is 14, home_entry at 50 direction is Counter-Clockwise
Piece Yellow got direction Clockwise
[Yellow] player moves piece Y2 to the starting point.
[Yellow] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 44
Piece Y2 -> Board position 0
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y2 from 0 to 0


Blue rolls 2
DEBUG: Piece Blue3 at pos 37, die_roll 2, dist_from_home is 21, dist_from_start is 30, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L37 to L35 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 36
Piece B3 -> Board position 35
Piece B4 -> Board position 45
BLUE moves piece B3 from 37 to 35


Red rolls 4
RED cannot move this turn.


Green rolls 3
DEBUG: Piece Green1 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 8, die_roll 3, dist_from_home is 17, dist_from_start is 34, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L8 to L5 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 5
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 8 to 5


Yellow rolls 4
DEBUG: Piece Yellow1 at pos 44, die_roll 4, dist_from_home is 39, dist_from_start is 12, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y1 from location L44 to L40 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 40
Piece Y2 -> Board position 0
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 44 to 40


Blue rolls 3
DEBUG: Piece Blue4 at pos 45, die_roll 3, dist_from_home is 28, dist_from_start is 23, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L45 to L42 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 36
Piece B3 -> Board position 35
Piece B4 -> Board position 42
BLUE moves piece B4 from 45 to 42


Red rolls 5
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 5, die_roll 2, dist_from_home is 15, dist_from_start is 36, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L5 to L3 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 3
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 5 to 3


Yellow rolls 4
DEBUG: Piece Yellow1 at pos 40, die_roll 4, dist_from_home is 35, dist_from_start is 16, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
BLOCK: [Yellow] piece Y1 was blocked - moved from L40 to L40 instead of intended L44 (blocked by opponent pieces).

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 36
Piece Y2 -> Board position 0
Piece Y3 -> Base
Piece Y4 -> Base
YELLOW moves piece Y1 from 40 to 40


Blue rolls 4
DEBUG: Piece Blue2 at pos 36, die_roll 4, dist_from_home is 24, dist_from_start is 27, home_entry at 11 direction is Clockwise
BLOCK: [Blue] piece B2 was blocked - moved from L36 to L38 instead of intended L40 (blocked by opponent pieces).

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 40
Piece B3 -> Board position 35
Piece B4 -> Board position 42
BLUE moves piece B2 from 36 to 38


Red rolls 5
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 3, die_roll 2, dist_from_home is 13, dist_from_start is 38, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L3 to L1 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 1
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 3 to 1


Yellow rolls 6
DEBUG: Piece Yellow1 at pos 36, die_roll 6, dist_from_home is 29, dist_from_start is 22, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Clockwise
Piece Yellow got direction Clockwise
[Yellow] player moves piece Y3 to the starting point.
[Yellow] player now has 3/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 36
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y3 from 0 to 0


Blue rolls 2
DEBUG: Piece Blue3 at pos 35, die_roll 2, dist_from_home is 19, dist_from_start is 32, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L35 to L33 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 40
Piece B3 -> Board position 33
Piece B4 -> Board position 42
BLUE moves piece B3 from 35 to 33


Red rolls 6
Piece Red got direction Clockwise
[Red] player moves piece R1 to the starting point.
[Red] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 26
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 0 to 26


Green rolls 5
DEBUG: Piece Green1 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 1, die_roll 5, dist_from_home is 8, dist_from_start is 43, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Clockwise
BLOCK: [Green] piece G2 was blocked - moved from L1 to L1 instead of intended L6 (blocked by opponent pieces).

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 48
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 1 to 1


Yellow rolls 2
DEBUG: Piece Yellow1 at pos 36, die_roll 2, dist_from_home is 33, dist_from_start is 18, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y1 from location L36 to L34 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 34
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 36 to 34


Blue rolls 1
DEBUG: Piece Blue4 at pos 42, die_roll 1, dist_from_home is 27, dist_from_start is 24, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L42 to L41 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 40
Piece B3 -> Board position 33
Piece B4 -> Board position 41
BLUE moves piece B4 from 42 to 41


Red rolls 1
DEBUG: Piece Red1 at pos 26, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L26 to L27 by 1 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 27
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 26 to 27


Green rolls 4
DEBUG: Piece Green1 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 48, die_roll 4, dist_from_home is 4, dist_from_start is 47, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L48 to L44 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 44
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 48 to 44


Yellow rolls 4
DEBUG: Piece Yellow1 at pos 34, die_roll 4, dist_from_home is 29, dist_from_start is 22, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y1 from location L34 to L30 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 30
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 34 to 30


Blue rolls 6
Piece Blue got direction Counter-Clockwise
[Blue] player moves piece B1 to the starting point.
[Blue] player now has 4/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Board position 40
Piece B3 -> Board position 33
Piece B4 -> Board position 41
BLUE moves piece B1 from 0 to 13


Red rolls 1
DEBUG: Piece Red1 at pos 27, die_roll 1, dist_from_home is 49, dist_from_start is 2, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L27 to L28 by 1 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 28
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 27 to 28


Green rolls 3
DEBUG: Piece Green1 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 44, die_roll 3, dist_from_home is 1, dist_from_start is 50, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
CAPTURE: [Green] piece G2 lands on square 41, captures [Blue] piece B4, and returns it to the base.
[Blue] player now has 3/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 39
Piece G2 -> Board position 41
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 44 to 41 and gets a bonus roll!
Player Green gets a bonus roll

Green rolls 2
DEBUG: Piece Green1 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 41, die_roll 2, dist_from_home is -1, dist_from_start is 52, home_entry at 37 direction is Counter-Clockwise
DEBUG: Crosses home entry at step 1, steps_into_home = 1
DEBUG: Entering home path at index 1
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L39 to L41 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 41
Piece G2 -> Board position 41
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G1 from 39 to 41


Yellow rolls 2
DEBUG: Piece Yellow1 at pos 30, die_roll 2, dist_from_home is 27, dist_from_start is 24, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
CAPTURE: [Yellow] piece Y1 lands on square 28, captures [Red] piece R1, and returns it to the base.
[Red] player now has 0/4 pieces on the board and 4/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 28
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 30 to 28 and gets a bonus roll!
Player Yellow gets a bonus roll

Yellow rolls 4
DEBUG: Piece Yellow1 at pos 28, die_roll 4, dist_from_home is 23, dist_from_start is 28, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y1 from location L28 to L24 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 24
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 28 to 24


Blue rolls 4
DEBUG: Piece Blue2 at pos 40, die_roll 4, dist_from_home is 20, dist_from_start is 31, home_entry at 11 direction is Clockwise
BLOCK: [Blue] piece B2 was blocked - moved from L40 to L40 instead of intended L44 (blocked by opponent pieces).

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Board position 44
Piece B3 -> Board position 33
Piece B4 -> Base
BLUE moves piece B2 from 40 to 40


Red rolls 2
RED cannot move this turn.


Green rolls 1
DEBUG: Piece Green1 at pos 41, die_roll 1, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 41, die_roll 1, dist_from_home is 0, dist_from_start is 51, home_entry at 37 direction is Counter-Clockwise
DEBUG: Crosses home entry at step 1, steps_into_home = 0
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
[Green] moves piece G2 from location L41 to L40 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 41
Piece G2 -> Board position 40
Piece G3 -> Board position 39
Piece G4 -> Board position 39
GREEN moves piece G2 from 41 to 40


Yellow rolls 4
DEBUG: Piece Yellow1 at pos 24, die_roll 4, dist_from_home is 19, dist_from_start is 32, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y1 from location L24 to L20 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 20
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 24 to 20


Blue rolls 4
DEBUG: Piece Blue3 at pos 33, die_roll 4, dist_from_home is 15, dist_from_start is 36, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L33 to L29 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Board position 44
Piece B3 -> Board position 29
Piece B4 -> Base
BLUE moves piece B3 from 33 to 29


Red rolls 1
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 41, die_roll 2, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 40, die_roll 2, dist_from_home is -2, dist_from_start is 53, home_entry at 37 direction is Counter-Clockwise
DEBUG: Crosses home entry at step 0, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L39 to L41 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 41
Piece G2 -> Board position 40
Piece G3 -> Board position 41
Piece G4 -> Board position 39
GREEN moves piece G3 from 39 to 41


Yellow rolls 5
DEBUG: Piece Yellow1 at pos 20, die_roll 5, dist_from_home is 14, dist_from_start is 37, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y1 from location L20 to L15 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 15
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 20 to 15


Blue rolls 6
Piece Blue got direction Counter-Clockwise
[Blue] player moves piece B4 to the starting point.
[Blue] player now has 4/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Board position 44
Piece B3 -> Board position 29
Piece B4 -> Board position 13
BLUE moves piece B4 from 0 to 13


Red rolls 1
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 41, die_roll 2, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 40, die_roll 2, dist_from_home is -2, dist_from_start is 53, home_entry at 37 direction is Counter-Clockwise
DEBUG: Crosses home entry at step 0, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Piece Green3 at pos 41, die_roll 2, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L39 to L41 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 41
Piece G2 -> Board position 40
Piece G3 -> Board position 41
Piece G4 -> Board position 41
GREEN moves piece G4 from 39 to 41


Yellow rolls 5
DEBUG: Piece Yellow1 at pos 15, die_roll 5, dist_from_home is 9, dist_from_start is 42, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
BLOCK: [Yellow] piece Y1 was blocked - moved from L15 to L14 instead of intended L20 (blocked by opponent pieces).

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 10
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 15 to 14


Blue rolls 5
DEBUG: Piece Blue1 at pos 13, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L13 to L8 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 8
Piece B2 -> Board position 44
Piece B3 -> Board position 29
Piece B4 -> Board position 13
BLUE moves piece B1 from 13 to 8


Red rolls 3
RED cannot move this turn.


Green rolls 5
DEBUG: Piece Green1 at pos 41, die_roll 5, dist_from_home is 44, dist_from_start is 7, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 40, die_roll 5, dist_from_home is -5, dist_from_start is 56, home_entry at 37 direction is Counter-Clockwise
DEBUG: Crosses home entry at step 0, steps_into_home = 5
DEBUG: Would overshoot home path (steps_into_home=5, HOME_PATH=5)
DEBUG: Piece Green3 at pos 41, die_roll 5, dist_from_home is 44, dist_from_start is 7, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 5, dist_from_home is 44, dist_from_start is 7, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L41 to L46 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 46
Piece G2 -> Board position 40
Piece G3 -> Board position 41
Piece G4 -> Board position 41
GREEN moves piece G1 from 41 to 46


Yellow rolls 4
DEBUG: Piece Yellow1 at pos 10, die_roll 4, dist_from_home is 5, dist_from_start is 46, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y1 from location L10 to L6 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 6
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 10 to 6


Blue rolls 1
DEBUG: Piece Blue2 at pos 44, die_roll 1, dist_from_home is 19, dist_from_start is 32, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L44 to L45 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 8
Piece B2 -> Board position 45
Piece B3 -> Board position 29
Piece B4 -> Board position 13
BLUE moves piece B2 from 44 to 45


Red rolls 2
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 46, die_roll 2, dist_from_home is 42, dist_from_start is 9, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 at pos 40, die_roll 2, dist_from_home is -2, dist_from_start is 53, home_entry at 37 direction is Counter-Clockwise
DEBUG: Crosses home entry at step 0, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Piece Green3 at pos 41, die_roll 2, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 2, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Applying move to home path, home_index = 1
[Green] moves piece G2 from location L40 to L1 by 13 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 46
Piece G2 -> Home path position 1
Piece G3 -> Board position 41
Piece G4 -> Board position 41
GREEN moves piece G2 from 40 to 1


Yellow rolls 1
DEBUG: Piece Yellow1 at pos 6, die_roll 1, dist_from_home is 4, dist_from_start is 47, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow2 at pos 0, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y1 from location L6 to L5 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Board position 5
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 6 to 5


Blue rolls 5
DEBUG: Piece Blue3 at pos 29, die_roll 5, dist_from_home is 10, dist_from_start is 41, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L29 to L24 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 8
Piece B2 -> Board position 45
Piece B3 -> Board position 24
Piece B4 -> Board position 13
BLUE moves piece B3 from 29 to 24


Red rolls 1
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green3 at pos 41, die_roll 6, dist_from_home is 43, dist_from_start is 8, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 6, dist_from_home is 43, dist_from_start is 8, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L41 to L47 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 46
Piece G2 -> Home path position 1
Piece G3 -> Board position 47
Piece G4 -> Board position 41
GREEN moves piece G3 from 41 to 47


Yellow rolls 5
DEBUG: Piece Yellow1 at pos 5, die_roll 5, dist_from_home is -1, dist_from_start is 52, home_entry at 50 direction is Counter-Clockwise
DEBUG: Crosses home entry at step 4, steps_into_home = 1
DEBUG: Entering home path at index 1
DEBUG: Piece Yellow2 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Applying move to home path, home_index = 0
[Yellow] moves piece Y1 from location L5 to L0 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home path position 0
Piece Y2 -> Board position 0
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y1 from 5 to 0


Blue rolls 2
DEBUG: Piece Blue4 at pos 13, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L13 to L11 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 8
Piece B2 -> Board position 45
Piece B3 -> Board position 24
Piece B4 -> Board position 11
BLUE moves piece B4 from 13 to 11


Red rolls 1
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 46, die_roll 2, dist_from_home is 42, dist_from_start is 9, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 47, die_roll 2, dist_from_home is 41, dist_from_start is 10, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 2, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L47 to L49 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 46
Piece G2 -> Home path position 1
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G3 from 47 to 49


Yellow rolls 2
DEBUG: Piece Yellow2 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L0 to L2 by 2 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home path position 0
Piece Y2 -> Board position 2
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y2 from 0 to 2


Blue rolls 2
DEBUG: Piece Blue1 at pos 8, die_roll 2, dist_from_home is 44, dist_from_start is 7, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L8 to L6 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 6
Piece B2 -> Board position 45
Piece B3 -> Board position 24
Piece B4 -> Board position 11
BLUE moves piece B1 from 8 to 6


Red rolls 2
RED cannot move this turn.


Green rolls 4
DEBUG: Piece Green1 at pos 46, die_roll 4, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Clockwise
DEBUG: Reaching final home position
DEBUG: Piece Green3 at pos 49, die_roll 4, dist_from_home is 37, dist_from_start is 14, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 4, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green2 reaches final home
[Green] piece G2 reaches home!
[Green] player now has 1/4 pieces at home.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 46
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G2 from 1 to 5


Yellow rolls 3
DEBUG: Piece Yellow2 at pos 2, die_roll 3, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L2 to L5 by 3 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home path position 0
Piece Y2 -> Board position 5
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y2 from 2 to 5


Blue rolls 2
DEBUG: Piece Blue2 at pos 45, die_roll 2, dist_from_home is 17, dist_from_start is 34, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L45 to L47 by 2 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 6
Piece B2 -> Board position 47
Piece B3 -> Board position 24
Piece B4 -> Board position 11
BLUE moves piece B2 from 45 to 47


Red rolls 4
RED cannot move this turn.


Green rolls 3
DEBUG: Piece Green1 at pos 46, die_roll 3, dist_from_home is 41, dist_from_start is 10, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 3, dist_from_home is 38, dist_from_start is 13, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 3, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L46 to L49 by 3 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 49
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 46 to 49


Yellow rolls 4
DEBUG: Piece Yellow2 at pos 5, die_roll 4, dist_from_home is 42, dist_from_start is 9, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L5 to L9 by 4 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home path position 0
Piece Y2 -> Board position 9
Piece Y3 -> Board position 0
Piece Y4 -> Base
YELLOW moves piece Y2 from 5 to 9


Blue rolls 5
DEBUG: Piece Blue3 at pos 24, die_roll 5, dist_from_home is 5, dist_from_start is 46, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L24 to L19 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 6
Piece B2 -> Board position 47
Piece B3 -> Board position 19
Piece B4 -> Board position 11
BLUE moves piece B3 from 24 to 19


Red rolls 3
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green1 at pos 49, die_roll 2, dist_from_home is 39, dist_from_start is 12, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 2, dist_from_home is 39, dist_from_start is 12, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 2, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L49 to L51 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 51
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 49 to 51


Yellow rolls 6
DEBUG: Piece Yellow2 at pos 9, die_roll 6, dist_from_home is 36, dist_from_start is 15, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Clockwise
Piece Yellow got direction Clockwise
[Yellow] player moves piece Y4 to the starting point.
[Yellow] player now has 3/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home path position 0
Piece Y2 -> Board position 9
Piece Y3 -> Board position 0
Piece Y4 -> Board position 0
YELLOW moves piece Y4 from 0 to 0


Blue rolls 1
DEBUG: Piece Blue4 at pos 11, die_roll 1, dist_from_home is 48, dist_from_start is 3, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L11 to L10 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 6
Piece B2 -> Board position 47
Piece B3 -> Board position 19
Piece B4 -> Board position 10
BLUE moves piece B4 from 11 to 10


Red rolls 5
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green1 at pos 51, die_roll 6, dist_from_home is 33, dist_from_start is 18, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 6, dist_from_home is 35, dist_from_start is 16, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 6, dist_from_home is 43, dist_from_start is 8, home_entry at 37 direction is Clockwise
BLOCK: [Green] piece G1 was blocked - moved from L51 to L51 instead of intended L5 (blocked by opponent pieces).

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 5
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 51 to 51


Yellow rolls 3
DEBUG: Piece Yellow2 at pos 9, die_roll 3, dist_from_home is 39, dist_from_start is 12, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L9 to L12 by 3 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home path position 0
Piece Y2 -> Board position 12
Piece Y3 -> Board position 0
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 9 to 12


Blue rolls 5
DEBUG: Piece Blue1 at pos 6, die_roll 5, dist_from_home is 39, dist_from_start is 12, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L6 to L1 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 1
Piece B2 -> Board position 47
Piece B3 -> Board position 19
Piece B4 -> Board position 10
BLUE moves piece B1 from 6 to 1


Red rolls 6
Piece Red got direction Clockwise
[Red] player moves piece R1 to the starting point.
[Red] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 26
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 0 to 26


Green rolls 2
DEBUG: Piece Green1 at pos 5, die_roll 2, dist_from_home is 31, dist_from_start is 20, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 2, dist_from_home is 39, dist_from_start is 12, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 2, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L5 to L7 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 7
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 5 to 7


Yellow rolls 5
DEBUG: Reaching final home position
DEBUG: Piece Yellow2 at pos 12, die_roll 5, dist_from_home is 34, dist_from_start is 17, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow1 reaches final home
[Yellow] piece Y1 reaches home!
[Yellow] player now has 1/4 pieces at home.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 12
Piece Y3 -> Board position 0
Piece Y4 -> Board position 0
YELLOW moves piece Y1 from 0 to 5


Blue rolls 4
DEBUG: Piece Blue2 at pos 47, die_roll 4, dist_from_home is 13, dist_from_start is 38, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L47 to L51 by 4 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 1
Piece B2 -> Board position 51
Piece B3 -> Board position 19
Piece B4 -> Board position 10
BLUE moves piece B2 from 47 to 51


Red rolls 2
DEBUG: Piece Red1 at pos 26, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L26 to L28 by 2 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 28
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 26 to 28


Green rolls 1
DEBUG: Piece Green1 at pos 7, die_roll 1, dist_from_home is 30, dist_from_start is 21, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 1, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 1, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L7 to L8 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 8
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 7 to 8


Yellow rolls 1
DEBUG: Piece Yellow2 at pos 12, die_roll 1, dist_from_home is 38, dist_from_start is 13, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 50 direction is Clockwise
CAPTURE: [Yellow] piece Y3 lands on square 1, captures [Blue] piece B1, and returns it to the base.
[Blue] player now has 3/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 12
Piece Y3 -> Board position 1
Piece Y4 -> Board position 0
YELLOW moves piece Y3 from 0 to 1 and gets a bonus roll!
Player Yellow gets a bonus roll

Yellow rolls 6
DEBUG: Piece Yellow2 at pos 12, die_roll 6, dist_from_home is 33, dist_from_start is 18, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 1, die_roll 6, dist_from_home is 44, dist_from_start is 7, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L12 to L18 by 6 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 18
Piece Y3 -> Board position 1
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 12 to 18


Blue rolls 4
DEBUG: Piece Blue3 at pos 19, die_roll 4, dist_from_home is 1, dist_from_start is 50, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L19 to L15 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 51
Piece B3 -> Board position 15
Piece B4 -> Board position 10
BLUE moves piece B3 from 19 to 15


Red rolls 6
DEBUG: Piece Red1 at pos 28, die_roll 6, dist_from_home is 43, dist_from_start is 8, home_entry at 24 direction is Clockwise
Piece Red got direction Clockwise
[Red] player moves piece R2 to the starting point.
[Red] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 28
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 0 to 26


Green rolls 6
DEBUG: Piece Green1 at pos 8, die_roll 6, dist_from_home is 24, dist_from_start is 27, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 6, dist_from_home is 35, dist_from_start is 16, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 6, dist_from_home is 43, dist_from_start is 8, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L8 to L14 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 14
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 8 to 14


Yellow rolls 1
DEBUG: Piece Yellow2 at pos 18, die_roll 1, dist_from_home is 32, dist_from_start is 19, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 1, die_roll 1, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L18 to L19 by 1 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 19
Piece Y3 -> Board position 1
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 18 to 19


Blue rolls 5
DEBUG: Piece Blue4 at pos 10, die_roll 5, dist_from_home is 43, dist_from_start is 8, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B4 from location L10 to L5 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 51
Piece B3 -> Board position 15
Piece B4 -> Board position 5
BLUE moves piece B4 from 10 to 5


Red rolls 3
DEBUG: Piece Red1 at pos 28, die_roll 3, dist_from_home is 46, dist_from_start is 5, home_entry at 24 direction is Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L28 to L31 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 31
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 28 to 31


Green rolls 1
DEBUG: Piece Green1 at pos 14, die_roll 1, dist_from_home is 23, dist_from_start is 28, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 1, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 1, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
CAPTURE: [Green] piece G1 lands on square 15, captures [Blue] piece B3, and returns it to the base.
[Blue] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 15
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 14 to 15 and gets a bonus roll!
Player Green gets a bonus roll

Green rolls 1
DEBUG: Piece Green1 at pos 15, die_roll 1, dist_from_home is 22, dist_from_start is 29, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 1, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 1, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L15 to L16 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 16
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 15 to 16


Yellow rolls 4
DEBUG: Piece Yellow2 at pos 19, die_roll 4, dist_from_home is 28, dist_from_start is 23, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 1, die_roll 4, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
CAPTURE: [Yellow] piece Y3 lands on square 5, captures [Blue] piece B4, and returns it to the base.
[Blue] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 19
Piece Y3 -> Board position 5
Piece Y4 -> Board position 0
YELLOW moves piece Y3 from 1 to 5 and gets a bonus roll!
Player Yellow gets a bonus roll

Yellow rolls 6
DEBUG: Piece Yellow2 at pos 19, die_roll 6, dist_from_home is 26, dist_from_start is 25, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 5, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L19 to L25 by 6 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 25
Piece Y3 -> Board position 5
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 19 to 25


Blue rolls 4
DEBUG: Piece Blue2 at pos 51, die_roll 4, dist_from_home is 9, dist_from_start is 42, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L51 to L3 by 4 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 3
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B2 from 51 to 3


Red rolls 4
DEBUG: Piece Red1 at pos 31, die_roll 4, dist_from_home is 42, dist_from_start is 9, home_entry at 24 direction is Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L31 to L35 by 4 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 35
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 31 to 35


Green rolls 6
DEBUG: Piece Green1 at pos 16, die_roll 6, dist_from_home is 16, dist_from_start is 35, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 6, dist_from_home is 35, dist_from_start is 16, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 6, dist_from_home is 43, dist_from_start is 8, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L16 to L22 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 22
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 16 to 22


Yellow rolls 6
DEBUG: Piece Yellow2 at pos 25, die_roll 6, dist_from_home is 20, dist_from_start is 31, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 5, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L25 to L31 by 6 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 31
Piece Y3 -> Board position 5
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 25 to 31


Blue rolls 1
DEBUG: Piece Blue2 at pos 3, die_roll 1, dist_from_home is 8, dist_from_start is 43, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L3 to L4 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 4
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B2 from 3 to 4


Red rolls 2
DEBUG: Piece Red1 at pos 35, die_roll 2, dist_from_home is 40, dist_from_start is 11, home_entry at 24 direction is Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L35 to L37 by 2 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 37
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 35 to 37


Green rolls 5
DEBUG: Piece Green1 at pos 22, die_roll 5, dist_from_home is 11, dist_from_start is 40, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 5, dist_from_home is 36, dist_from_start is 15, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 5, dist_from_home is 44, dist_from_start is 7, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L22 to L27 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 27
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 41
GREEN moves piece G1 from 22 to 27


Yellow rolls 2
DEBUG: Piece Yellow2 at pos 31, die_roll 2, dist_from_home is 18, dist_from_start is 33, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 5, die_roll 2, dist_from_home is 44, dist_from_start is 7, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L31 to L33 by 2 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 33
Piece Y3 -> Board position 5
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 31 to 33


Blue rolls 6
Piece Blue got direction Clockwise
[Blue] player moves piece B3 to the starting point.
[Blue] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 4
Piece B3 -> Board position 13
Piece B4 -> Base
BLUE moves piece B3 from 0 to 13


Red rolls 4
DEBUG: Piece Red1 at pos 37, die_roll 4, dist_from_home is 36, dist_from_start is 15, home_entry at 24 direction is Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 24 direction is Clockwise
CAPTURE: [Red] piece R1 lands on square 41, captures [Green] piece G4, and returns it to the base.
[Green] player now has 2/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 41
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 37 to 41 and gets a bonus roll!
Player Red gets a bonus roll

Red rolls 6
DEBUG: Piece Red1 at pos 41, die_roll 6, dist_from_home is 30, dist_from_start is 21, home_entry at 24 direction is Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L41 to L47 by 6 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 47
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 41 to 47


Green rolls 4
DEBUG: Piece Green1 at pos 27, die_roll 4, dist_from_home is 7, dist_from_start is 44, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 4, dist_from_home is 37, dist_from_start is 14, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L27 to L31 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 31
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Base
GREEN moves piece G1 from 27 to 31


Yellow rolls 2
DEBUG: Piece Yellow2 at pos 33, die_roll 2, dist_from_home is 16, dist_from_start is 35, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 5, die_roll 2, dist_from_home is 44, dist_from_start is 7, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L33 to L35 by 2 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 35
Piece Y3 -> Board position 5
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 33 to 35


Blue rolls 6
Piece Blue got direction Clockwise
[Blue] player moves piece B4 to the starting point.
[Blue] player now has 3/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 4
Piece B3 -> Board position 13
Piece B4 -> Board position 13
BLUE moves piece B4 from 0 to 13


Red rolls 6
DEBUG: Piece Red1 at pos 47, die_roll 6, dist_from_home is 24, dist_from_start is 27, home_entry at 24 direction is Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L47 to L1 by 6 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 1
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 47 to 1


Green rolls 2
DEBUG: Piece Green1 at pos 31, die_roll 2, dist_from_home is 5, dist_from_start is 46, home_entry at 37 direction is Clockwise
DEBUG: Piece Green3 at pos 49, die_roll 2, dist_from_home is 39, dist_from_start is 12, home_entry at 37 direction is Clockwise
[Green] moves piece G1 from location L31 to L33 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 33
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Base
GREEN moves piece G1 from 31 to 33


Yellow rolls 5
DEBUG: Piece Yellow2 at pos 35, die_roll 5, dist_from_home is 11, dist_from_start is 40, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow3 at pos 5, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L35 to L40 by 5 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 40
Piece Y3 -> Board position 5
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 35 to 40


Blue rolls 1
DEBUG: Piece Blue2 at pos 4, die_roll 1, dist_from_home is 7, dist_from_start is 44, home_entry at 11 direction is Clockwise
CAPTURE: [Blue] piece B2 lands on square 5, captures [Yellow] piece Y3, and returns it to the base.
[Yellow] player now has 2/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 5
Piece B3 -> Board position 13
Piece B4 -> Board position 13
BLUE moves piece B2 from 4 to 5 and gets a bonus roll!
Player Blue gets a bonus roll

Blue rolls 5
DEBUG: Piece Blue3 at pos 13, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L13 to L18 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 5
Piece B3 -> Board position 18
Piece B4 -> Board position 13
BLUE moves piece B3 from 13 to 18


Red rolls 1
DEBUG: Piece Red1 at pos 1, die_roll 1, dist_from_home is 23, dist_from_start is 28, home_entry at 24 direction is Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L1 to L2 by 1 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 2
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 1 to 2


Green rolls 6
DEBUG: Piece Green1 at pos 33, die_roll 6, dist_from_home is -1, dist_from_start is 52, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 5, steps_into_home = 1
DEBUG: Entering home path at index 1
DEBUG: Piece Green3 at pos 49, die_roll 6, dist_from_home is 35, dist_from_start is 16, home_entry at 37 direction is Clockwise
Piece Green got direction Counter-Clockwise
[Green] player moves piece G4 to the starting point.
[Green] player now has 3/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 33
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 39
GREEN moves piece G4 from 0 to 39


Yellow rolls 4
DEBUG: Piece Yellow2 at pos 40, die_roll 4, dist_from_home is 7, dist_from_start is 44, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y2 from location L40 to L44 by 4 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 44
Piece Y3 -> Base
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 40 to 44


Blue rolls 1
DEBUG: Piece Blue4 at pos 13, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L13 to L14 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 5
Piece B3 -> Board position 18
Piece B4 -> Board position 14
BLUE moves piece B4 from 13 to 14


Red rolls 6
DEBUG: Piece Red1 at pos 2, die_roll 6, dist_from_home is 17, dist_from_start is 34, home_entry at 24 direction is Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L2 to L8 by 6 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 8
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 2 to 8


Green rolls 6
DEBUG: Piece Green1 at pos 33, die_roll 6, dist_from_home is -1, dist_from_start is 52, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 5, steps_into_home = 1
DEBUG: Entering home path at index 1
DEBUG: Piece Green3 at pos 49, die_roll 6, dist_from_home is 35, dist_from_start is 16, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L39 to L33 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 33
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Board position 33
GREEN moves piece G4 from 39 to 33


Yellow rolls 5
DEBUG: Piece Yellow2 at pos 44, die_roll 5, dist_from_home is 2, dist_from_start is 49, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow4 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
CAPTURE: [Yellow] piece Y2 lands on square 49, captures [Green] piece G3, and returns it to the base.
[Green] player now has 2/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Board position 49
Piece Y3 -> Base
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 44 to 49 and gets a bonus roll!
Player Yellow gets a bonus roll

Yellow rolls 4
DEBUG: Piece Yellow2 at pos 49, die_roll 4, dist_from_home is -2, dist_from_start is 53, home_entry at 50 direction is Clockwise
DEBUG: Crosses home entry at step 2, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Piece Yellow4 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Clockwise
DEBUG: Applying move to home path, home_index = 1
[Yellow] moves piece Y2 from location L49 to L1 by 4 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home path position 1
Piece Y3 -> Base
Piece Y4 -> Board position 0
YELLOW moves piece Y2 from 49 to 1


Blue rolls 3
DEBUG: Piece Blue2 at pos 5, die_roll 3, dist_from_home is 4, dist_from_start is 47, home_entry at 11 direction is Clockwise
CAPTURE: [Blue] piece B2 lands on square 8, captures [Red] piece R1, and returns it to the base.
[Red] player now has 1/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 8
Piece B3 -> Board position 18
Piece B4 -> Board position 14
BLUE moves piece B2 from 5 to 8 and gets a bonus roll!
Player Blue gets a bonus roll

Blue rolls 4
DEBUG: Piece Blue3 at pos 18, die_roll 4, dist_from_home is 42, dist_from_start is 9, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L18 to L22 by 4 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 8
Piece B3 -> Board position 22
Piece B4 -> Board position 14
BLUE moves piece B3 from 18 to 22


Red rolls 5
DEBUG: Piece Red2 at pos 26, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L26 to L31 by 5 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 31
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 26 to 31


Green rolls 3
DEBUG: Piece Green1 at pos 33, die_roll 3, dist_from_home is 2, dist_from_start is 49, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 33, die_roll 3, dist_from_home is 42, dist_from_start is 9, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L33 to L36 by 3 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 36
Piece G2 -> Home
Piece G3 -> Base
Piece G4 -> Board position 33
GREEN moves piece G1 from 33 to 36


Yellow rolls 1
DEBUG: Piece Yellow4 at pos 0, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L0 to L1 by 1 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home path position 1
Piece Y3 -> Base
Piece Y4 -> Board position 1
YELLOW moves piece Y4 from 0 to 1


Blue rolls 5
DEBUG: Piece Blue4 at pos 14, die_roll 5, dist_from_home is 45, dist_from_start is 6, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L14 to L19 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 8
Piece B3 -> Board position 22
Piece B4 -> Board position 19
BLUE moves piece B4 from 14 to 19


Red rolls 3
DEBUG: Piece Red2 at pos 31, die_roll 3, dist_from_home is 43, dist_from_start is 8, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L31 to L34 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 34
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 31 to 34


Green rolls 6
DEBUG: Piece Green1 at pos 36, die_roll 6, dist_from_home is -4, dist_from_start is 55, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 2, steps_into_home = 4
DEBUG: Entering home path at index 4
DEBUG: Piece Green4 at pos 33, die_roll 6, dist_from_home is 39, dist_from_start is 12, home_entry at 37 direction is Counter-Clockwise
Piece Green got direction Clockwise
[Green] player moves piece G3 to the starting point.
[Green] player now has 3/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 36
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 33
GREEN moves piece G3 from 0 to 39


Yellow rolls 4
DEBUG: Reaching final home position
DEBUG: Piece Yellow4 at pos 1, die_roll 4, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
DEBUG: Piece Yellow2 reaches final home
[Yellow] piece Y2 reaches home!
[Yellow] player now has 2/4 pieces at home.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Base
Piece Y4 -> Board position 1
YELLOW moves piece Y2 from 1 to 5


Blue rolls 1
DEBUG: Piece Blue2 at pos 8, die_roll 1, dist_from_home is 3, dist_from_start is 48, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L8 to L9 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 9
Piece B3 -> Board position 22
Piece B4 -> Board position 19
BLUE moves piece B2 from 8 to 9


Red rolls 3
DEBUG: Piece Red2 at pos 34, die_roll 3, dist_from_home is 40, dist_from_start is 11, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L34 to L37 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 37
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 34 to 37


Green rolls 2
DEBUG: Piece Green1 at pos 36, die_roll 2, dist_from_home is 0, dist_from_start is 51, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 2, steps_into_home = 0
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 33, die_roll 2, dist_from_home is 43, dist_from_start is 8, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G1 from location L36 to L38 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Board position 38
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 33
GREEN moves piece G1 from 36 to 38


Yellow rolls 1
DEBUG: Piece Yellow4 at pos 1, die_roll 1, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L1 to L2 by 1 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Base
Piece Y4 -> Board position 2
YELLOW moves piece Y4 from 1 to 2


Blue rolls 3
DEBUG: Piece Blue3 at pos 22, die_roll 3, dist_from_home is 39, dist_from_start is 12, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L22 to L25 by 3 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 9
Piece B3 -> Board position 25
Piece B4 -> Board position 19
BLUE moves piece B3 from 22 to 25


Red rolls 4
DEBUG: Piece Red2 at pos 37, die_roll 4, dist_from_home is 36, dist_from_start is 15, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L37 to L41 by 4 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 41
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 37 to 41


Green rolls 3
DEBUG: Piece Green1 at pos 38, die_roll 3, dist_from_home is -3, dist_from_start is 54, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 0, steps_into_home = 3
DEBUG: Entering home path at index 3
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 33, die_roll 3, dist_from_home is 42, dist_from_start is 9, home_entry at 37 direction is Counter-Clockwise
DEBUG: Applying move to home path, home_index = 2
[Green] moves piece G1 from location L38 to L2 by 16 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home path position 2
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 33
GREEN moves piece G1 from 38 to 2


Yellow rolls 1
DEBUG: Piece Yellow4 at pos 2, die_roll 1, dist_from_home is 48, dist_from_start is 3, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L2 to L3 by 1 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Base
Piece Y4 -> Board position 3
YELLOW moves piece Y4 from 2 to 3


Blue rolls 5
DEBUG: Piece Blue4 at pos 19, die_roll 5, dist_from_home is 40, dist_from_start is 11, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L19 to L24 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 9
Piece B3 -> Board position 25
Piece B4 -> Board position 24
BLUE moves piece B4 from 19 to 24


Red rolls 4
DEBUG: Piece Red2 at pos 41, die_roll 4, dist_from_home is 32, dist_from_start is 19, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L41 to L45 by 4 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 45
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 41 to 45


Green rolls 4
DEBUG: Piece Green3 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 33, die_roll 4, dist_from_home is 41, dist_from_start is 10, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L33 to L29 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home path position 2
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 29
GREEN moves piece G4 from 33 to 29


Yellow rolls 2
DEBUG: Piece Yellow4 at pos 3, die_roll 2, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L3 to L5 by 2 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Base
Piece Y4 -> Board position 5
YELLOW moves piece Y4 from 3 to 5


Blue rolls 1
DEBUG: Piece Blue2 at pos 9, die_roll 1, dist_from_home is 2, dist_from_start is 49, home_entry at 11 direction is Clockwise
[Blue] moves piece B2 from location L9 to L10 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 10
Piece B3 -> Board position 25
Piece B4 -> Board position 24
BLUE moves piece B2 from 9 to 10


Red rolls 3
DEBUG: Piece Red2 at pos 45, die_roll 3, dist_from_home is 29, dist_from_start is 22, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L45 to L48 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 48
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 45 to 48


Green rolls 1
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 29, die_roll 1, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L29 to L28 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home path position 2
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 28
GREEN moves piece G4 from 29 to 28


Yellow rolls 4
DEBUG: Piece Yellow4 at pos 5, die_roll 4, dist_from_home is 42, dist_from_start is 9, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L5 to L9 by 4 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Base
Piece Y4 -> Board position 9
YELLOW moves piece Y4 from 5 to 9


Blue rolls 5
DEBUG: Piece Blue3 at pos 25, die_roll 5, dist_from_home is 34, dist_from_start is 17, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L25 to L30 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 10
Piece B3 -> Board position 30
Piece B4 -> Board position 24
BLUE moves piece B3 from 25 to 30


Red rolls 1
DEBUG: Piece Red2 at pos 48, die_roll 1, dist_from_home is 28, dist_from_start is 23, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L48 to L49 by 1 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 49
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 48 to 49


Green rolls 1
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 28, die_roll 1, dist_from_home is 39, dist_from_start is 12, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L28 to L27 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home path position 2
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 27
GREEN moves piece G4 from 28 to 27


Yellow rolls 6
DEBUG: Piece Yellow4 at pos 9, die_roll 6, dist_from_home is 36, dist_from_start is 15, home_entry at 50 direction is Clockwise
Piece Yellow got direction Counter-Clockwise
[Yellow] player moves piece Y3 to the starting point.
[Yellow] player now has 2/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 9
YELLOW moves piece Y3 from 0 to 0


Blue rolls 1
DEBUG: Piece Blue4 at pos 24, die_roll 1, dist_from_home is 39, dist_from_start is 12, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L24 to L25 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Board position 10
Piece B3 -> Board position 30
Piece B4 -> Board position 25
BLUE moves piece B4 from 24 to 25


Red rolls 2
DEBUG: Piece Red2 at pos 49, die_roll 2, dist_from_home is 26, dist_from_start is 25, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L49 to L51 by 2 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 51
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 49 to 51


Green rolls 1
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 27, die_roll 1, dist_from_home is 38, dist_from_start is 13, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L27 to L26 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home path position 2
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 26
GREEN moves piece G4 from 27 to 26


Yellow rolls 4
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 9, die_roll 4, dist_from_home is 38, dist_from_start is 13, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L9 to L13 by 4 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 13
YELLOW moves piece Y4 from 9 to 13


Blue rolls 5
DEBUG: Piece Blue2 at pos 10, die_roll 5, dist_from_home is -3, dist_from_start is 54, home_entry at 11 direction is Clockwise
DEBUG: Crosses home entry at step 2, steps_into_home = 3
DEBUG: Entering home path at index 3
DEBUG: Applying move to home path, home_index = 2
[Blue] moves piece B2 from location L10 to L2 by 44 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home path position 2
Piece B3 -> Board position 30
Piece B4 -> Board position 25
BLUE moves piece B2 from 10 to 2


Red rolls 4
DEBUG: Piece Red2 at pos 51, die_roll 4, dist_from_home is 22, dist_from_start is 29, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L51 to L3 by 4 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 3
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 51 to 3


Green rolls 3
DEBUG: Reaching final home position
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 26, die_roll 3, dist_from_home is 35, dist_from_start is 16, home_entry at 37 direction is Counter-Clockwise
DEBUG: Piece Green1 reaches final home
[Green] piece G1 reaches home!
[Green] player now has 2/4 pieces at home.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 26
GREEN moves piece G1 from 2 to 5


Yellow rolls 3
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 13, die_roll 3, dist_from_home is 35, dist_from_start is 16, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L13 to L16 by 3 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 16
YELLOW moves piece Y4 from 13 to 16


Blue rolls 2
DEBUG: Piece Blue3 at pos 30, die_roll 2, dist_from_home is 32, dist_from_start is 19, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L30 to L32 by 2 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home path position 2
Piece B3 -> Board position 32
Piece B4 -> Board position 25
BLUE moves piece B3 from 30 to 32


Red rolls 5
DEBUG: Piece Red2 at pos 3, die_roll 5, dist_from_home is 17, dist_from_start is 34, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L3 to L8 by 5 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 8
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 3 to 8


Green rolls 6
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 26, die_roll 6, dist_from_home is 32, dist_from_start is 19, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L26 to L20 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 20
GREEN moves piece G4 from 26 to 20


Yellow rolls 2
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 16, die_roll 2, dist_from_home is 33, dist_from_start is 18, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L16 to L18 by 2 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 18
YELLOW moves piece Y4 from 16 to 18


Blue rolls 3
DEBUG: Piece Blue4 at pos 25, die_roll 3, dist_from_home is 36, dist_from_start is 15, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L25 to L28 by 3 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home path position 2
Piece B3 -> Board position 32
Piece B4 -> Board position 28
BLUE moves piece B4 from 25 to 28


Red rolls 1
DEBUG: Piece Red2 at pos 8, die_roll 1, dist_from_home is 16, dist_from_start is 35, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L8 to L9 by 1 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 9
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 8 to 9


Green rolls 3
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 20, die_roll 3, dist_from_home is 29, dist_from_start is 22, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L20 to L17 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 17
GREEN moves piece G4 from 20 to 17


Yellow rolls 6
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 18, die_roll 6, dist_from_home is 27, dist_from_start is 24, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L18 to L24 by 6 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 24
YELLOW moves piece Y4 from 18 to 24


Blue rolls 1
DEBUG: Piece Blue3 at pos 32, die_roll 1, dist_from_home is 31, dist_from_start is 20, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L32 to L33 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home path position 2
Piece B3 -> Board position 33
Piece B4 -> Board position 28
BLUE moves piece B3 from 32 to 33


Red rolls 2
DEBUG: Piece Red2 at pos 9, die_roll 2, dist_from_home is 14, dist_from_start is 37, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L9 to L11 by 2 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Base
Piece R2 -> Board position 11
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 9 to 11


Green rolls 6
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 17, die_roll 6, dist_from_home is 23, dist_from_start is 28, home_entry at 37 direction is Counter-Clockwise
CAPTURE: [Green] piece G4 lands on square 11, captures [Red] piece R2, and returns it to the base.
[Red] player now has 0/4 pieces on the board and 4/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 11
GREEN moves piece G4 from 17 to 11 and gets a bonus roll!
Player Green gets a bonus roll

Green rolls 5
DEBUG: Piece Green3 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 11, die_roll 5, dist_from_home is 18, dist_from_start is 33, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L11 to L6 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 6
GREEN moves piece G4 from 11 to 6


Yellow rolls 6
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 24, die_roll 6, dist_from_home is 21, dist_from_start is 30, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L24 to L30 by 6 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 30
YELLOW moves piece Y4 from 24 to 30


Blue rolls 5
DEBUG: Piece Blue4 at pos 28, die_roll 5, dist_from_home is 31, dist_from_start is 20, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L28 to L33 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home path position 2
Piece B3 -> Board position 33
Piece B4 -> Board position 33
BLUE moves piece B4 from 28 to 33


Red rolls 1
RED cannot move this turn.


Green rolls 3
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 6, die_roll 3, dist_from_home is 15, dist_from_start is 36, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L6 to L3 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 3
GREEN moves piece G4 from 6 to 3


Yellow rolls 6
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 30, die_roll 6, dist_from_home is 15, dist_from_start is 36, home_entry at 50 direction is Clockwise
BLOCK: [Yellow] piece Y4 was blocked - moved from L30 to L32 instead of intended L36 (blocked by opponent pieces).

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 36
YELLOW moves piece Y4 from 30 to 32


Blue rolls 5
DEBUG: Piece Blue3 at pos 33, die_roll 5, dist_from_home is 26, dist_from_start is 25, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L33 to L38 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home path position 2
Piece B3 -> Board position 38
Piece B4 -> Board position 33
BLUE moves piece B3 from 33 to 38


Red rolls 2
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 3, die_roll 2, dist_from_home is 13, dist_from_start is 38, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L3 to L1 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 1
GREEN moves piece G4 from 3 to 1


Yellow rolls 5
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 36, die_roll 5, dist_from_home is 10, dist_from_start is 41, home_entry at 50 direction is Clockwise
[Yellow] moves piece Y4 from location L36 to L41 by 5 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 41
YELLOW moves piece Y4 from 36 to 41


Blue rolls 5
DEBUG: Piece Blue4 at pos 33, die_roll 5, dist_from_home is 26, dist_from_start is 25, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L33 to L38 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home path position 2
Piece B3 -> Board position 38
Piece B4 -> Board position 38
BLUE moves piece B4 from 33 to 38


Red rolls 2
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 1, die_roll 6, dist_from_home is 7, dist_from_start is 44, home_entry at 37 direction is Counter-Clockwise
[Green] moves piece G4 from location L1 to L47 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 47
GREEN moves piece G4 from 1 to 47


Yellow rolls 6
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 41, die_roll 6, dist_from_home is 4, dist_from_start is 47, home_entry at 50 direction is Clockwise
CAPTURE: [Yellow] piece Y4 lands on square 47, captures [Green] piece G4, and returns it to the base.
[Green] player now has 1/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Board position 47
YELLOW moves piece Y4 from 41 to 47 and gets a bonus roll!
Player Yellow gets a bonus roll

Yellow rolls 5
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Counter-Clockwise
DEBUG: Piece Yellow4 at pos 47, die_roll 5, dist_from_home is -1, dist_from_start is 52, home_entry at 50 direction is Clockwise
DEBUG: Crosses home entry at step 4, steps_into_home = 1
DEBUG: Entering home path at index 1
DEBUG: Applying move to home path, home_index = 0
[Yellow] moves piece Y4 from location L47 to L0 by 5 units in Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Home path position 0
YELLOW moves piece Y4 from 47 to 0


Blue rolls 6
Piece Blue got direction Counter-Clockwise
[Blue] player moves piece B1 to the starting point.
[Blue] player now has 3/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Home path position 2
Piece B3 -> Board position 38
Piece B4 -> Board position 38
BLUE moves piece B1 from 0 to 13


Red rolls 5
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L39 to L41 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 41
Piece G4 -> Base
GREEN moves piece G3 from 39 to 41


Yellow rolls 4
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L0 to L48 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 48
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 0 to 48


Blue rolls 2
DEBUG: Piece Blue3 at pos 38, die_roll 2, dist_from_home is 24, dist_from_start is 27, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L38 to L40 by 2 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Home path position 2
Piece B3 -> Board position 40
Piece B4 -> Board position 38
BLUE moves piece B3 from 38 to 40


Red rolls 1
RED cannot move this turn.


Green rolls 1
DEBUG: Piece Green3 at pos 41, die_roll 1, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L41 to L42 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 42
Piece G4 -> Base
GREEN moves piece G3 from 41 to 42


Yellow rolls 4
DEBUG: Piece Yellow3 at pos 48, die_roll 4, dist_from_home is 43, dist_from_start is 8, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L48 to L44 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 44
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 48 to 44


Blue rolls 1
DEBUG: Piece Blue4 at pos 38, die_roll 1, dist_from_home is 25, dist_from_start is 26, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L38 to L39 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Home path position 2
Piece B3 -> Board position 40
Piece B4 -> Board position 39
BLUE moves piece B4 from 38 to 39


Red rolls 5
RED cannot move this turn.


Green rolls 5
DEBUG: Piece Green3 at pos 42, die_roll 5, dist_from_home is 43, dist_from_start is 8, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L42 to L47 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 47
Piece G4 -> Base
GREEN moves piece G3 from 42 to 47


Yellow rolls 1
DEBUG: Piece Yellow3 at pos 44, die_roll 1, dist_from_home is 42, dist_from_start is 9, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L44 to L43 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 43
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 44 to 43


Blue rolls 3
DEBUG: Piece Blue1 at pos 13, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L13 to L10 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 10
Piece B2 -> Home path position 2
Piece B3 -> Board position 40
Piece B4 -> Board position 39
BLUE moves piece B1 from 13 to 10


Red rolls 1
RED cannot move this turn.


Green rolls 3
DEBUG: Piece Green3 at pos 47, die_roll 3, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L47 to L50 by 3 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 50
Piece G4 -> Base
GREEN moves piece G3 from 47 to 50


Yellow rolls 1
DEBUG: Piece Yellow3 at pos 43, die_roll 1, dist_from_home is 41, dist_from_start is 10, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L43 to L42 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 42
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 43 to 42


Blue rolls 5
DEBUG: Piece Blue3 at pos 40, die_roll 5, dist_from_home is 19, dist_from_start is 32, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L40 to L45 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 10
Piece B2 -> Home path position 2
Piece B3 -> Board position 45
Piece B4 -> Board position 39
BLUE moves piece B3 from 40 to 45


Red rolls 5
RED cannot move this turn.


Green rolls 4
DEBUG: Piece Green3 at pos 50, die_roll 4, dist_from_home is 36, dist_from_start is 15, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L50 to L2 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 2
Piece G4 -> Base
GREEN moves piece G3 from 50 to 2


Yellow rolls 1
DEBUG: Piece Yellow3 at pos 42, die_roll 1, dist_from_home is 40, dist_from_start is 11, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L42 to L41 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 41
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 42 to 41


Blue rolls 1
DEBUG: Piece Blue4 at pos 39, die_roll 1, dist_from_home is 24, dist_from_start is 27, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L39 to L40 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 10
Piece B2 -> Home path position 2
Piece B3 -> Board position 45
Piece B4 -> Board position 40
BLUE moves piece B4 from 39 to 40


Red rolls 3
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green3 at pos 2, die_roll 6, dist_from_home is 30, dist_from_start is 21, home_entry at 37 direction is Clockwise
Piece Green got direction Clockwise
[Green] player moves piece G4 to the starting point.
[Green] player now has 2/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 2
Piece G4 -> Board position 39
GREEN moves piece G4 from 0 to 39


Yellow rolls 4
DEBUG: Piece Yellow3 at pos 41, die_roll 4, dist_from_home is 36, dist_from_start is 15, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L41 to L37 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 37
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 41 to 37


Blue rolls 5
DEBUG: Piece Blue1 at pos 10, die_roll 5, dist_from_home is 43, dist_from_start is 8, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L10 to L5 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 5
Piece B2 -> Home path position 2
Piece B3 -> Board position 45
Piece B4 -> Board position 40
BLUE moves piece B1 from 10 to 5


Red rolls 3
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green3 at pos 2, die_roll 2, dist_from_home is 34, dist_from_start is 17, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L2 to L4 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 4
Piece G4 -> Board position 39
GREEN moves piece G3 from 2 to 4


Yellow rolls 3
DEBUG: Piece Yellow3 at pos 37, die_roll 3, dist_from_home is 33, dist_from_start is 18, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L37 to L34 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 34
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 37 to 34


Blue rolls 3
DEBUG: Reaching final home position
DEBUG: Piece Blue2 reaches final home
[Blue] piece B2 reaches home!
[Blue] player now has 1/4 pieces at home.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 5
Piece B2 -> Home
Piece B3 -> Board position 45
Piece B4 -> Board position 40
BLUE moves piece B2 from 2 to 5


Red rolls 4
RED cannot move this turn.


Green rolls 4
DEBUG: Piece Green3 at pos 4, die_roll 4, dist_from_home is 30, dist_from_start is 21, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L4 to L8 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 8
Piece G4 -> Board position 39
GREEN moves piece G3 from 4 to 8


Yellow rolls 2
DEBUG: Piece Yellow3 at pos 34, die_roll 2, dist_from_home is 31, dist_from_start is 20, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L34 to L32 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 32
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 34 to 32


Blue rolls 1
DEBUG: Piece Blue3 at pos 45, die_roll 1, dist_from_home is 18, dist_from_start is 33, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L45 to L46 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 5
Piece B2 -> Home
Piece B3 -> Board position 46
Piece B4 -> Board position 40
BLUE moves piece B3 from 45 to 46


Red rolls 4
RED cannot move this turn.


Green rolls 6
DEBUG: Piece Green3 at pos 8, die_roll 6, dist_from_home is 24, dist_from_start is 27, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L8 to L14 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 14
Piece G4 -> Board position 39
GREEN moves piece G3 from 8 to 14


Yellow rolls 6
DEBUG: Piece Yellow3 at pos 32, die_roll 6, dist_from_home is 25, dist_from_start is 26, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L32 to L26 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 26
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 32 to 26


Blue rolls 2
DEBUG: Piece Blue4 at pos 40, die_roll 2, dist_from_home is 22, dist_from_start is 29, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L40 to L42 by 2 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 5
Piece B2 -> Home
Piece B3 -> Board position 46
Piece B4 -> Board position 42
BLUE moves piece B4 from 40 to 42


Red rolls 6
Piece Red got direction Counter-Clockwise
CAPTURE: [Red] piece R1 lands on square 26, captures [Yellow] piece Y3, and returns it to the base.
[Yellow] player now has 0/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 26
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 0 to 26 and gets a bonus roll!
Player Red gets a bonus roll

Red rolls 5
DEBUG: Piece Red1 at pos 26, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 24 direction is Counter-Clockwise
[Red] moves piece R1 from location L26 to L21 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 21
Piece R2 -> Base
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 26 to 21


Green rolls 4
DEBUG: Piece Green3 at pos 14, die_roll 4, dist_from_home is 20, dist_from_start is 31, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L14 to L18 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 18
Piece G4 -> Board position 39
GREEN moves piece G3 from 14 to 18


Yellow rolls 4
YELLOW cannot move this turn.


Blue rolls 3
DEBUG: Piece Blue1 at pos 5, die_roll 3, dist_from_home is 40, dist_from_start is 11, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L5 to L2 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 2
Piece B2 -> Home
Piece B3 -> Board position 46
Piece B4 -> Board position 42
BLUE moves piece B1 from 5 to 2


Red rolls 6
DEBUG: Piece Red1 at pos 21, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 24 direction is Counter-Clockwise
Piece Red got direction Clockwise
[Red] player moves piece R2 to the starting point.
[Red] player now has 2/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 21
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 0 to 26


Green rolls 2
DEBUG: Piece Green3 at pos 18, die_roll 2, dist_from_home is 18, dist_from_start is 33, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L18 to L20 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 20
Piece G4 -> Board position 39
GREEN moves piece G3 from 18 to 20


Yellow rolls 4
YELLOW cannot move this turn.


Blue rolls 2
DEBUG: Piece Blue3 at pos 46, die_roll 2, dist_from_home is 16, dist_from_start is 35, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L46 to L48 by 2 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 2
Piece B2 -> Home
Piece B3 -> Board position 48
Piece B4 -> Board position 42
BLUE moves piece B3 from 46 to 48


Red rolls 6
DEBUG: Piece Red1 at pos 21, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L21 to L15 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 15
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 21 to 15


Green rolls 4
DEBUG: Piece Green3 at pos 20, die_roll 4, dist_from_home is 14, dist_from_start is 37, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L20 to L24 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 24
Piece G4 -> Board position 39
GREEN moves piece G3 from 20 to 24


Yellow rolls 6
Piece Yellow got direction Counter-Clockwise
[Yellow] player moves piece Y3 to the starting point.
[Yellow] player now has 1/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 0 to 0


Blue rolls 3
DEBUG: Piece Blue4 at pos 42, die_roll 3, dist_from_home is 19, dist_from_start is 32, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L42 to L45 by 3 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 2
Piece B2 -> Home
Piece B3 -> Board position 48
Piece B4 -> Board position 45
BLUE moves piece B4 from 42 to 45


Red rolls 1
DEBUG: Piece Red1 at pos 15, die_roll 1, dist_from_home is 39, dist_from_start is 12, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L15 to L14 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 14
Piece R2 -> Board position 26
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 15 to 14


Green rolls 3
DEBUG: Piece Green3 at pos 24, die_roll 3, dist_from_home is 11, dist_from_start is 40, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L24 to L27 by 3 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 27
Piece G4 -> Board position 39
GREEN moves piece G3 from 24 to 27


Yellow rolls 2
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L0 to L50 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 50
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 0 to 50


Blue rolls 5
DEBUG: Piece Blue1 at pos 2, die_roll 5, dist_from_home is 35, dist_from_start is 16, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L2 to L49 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 49
Piece B2 -> Home
Piece B3 -> Board position 48
Piece B4 -> Board position 45
BLUE moves piece B1 from 2 to 49


Red rolls 1
DEBUG: Piece Red1 at pos 14, die_roll 1, dist_from_home is 38, dist_from_start is 13, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 26, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 24 direction is Clockwise
CAPTURE: [Red] piece R2 lands on square 27, captures [Green] piece G3, and returns it to the base.
[Green] player now has 1/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 14
Piece R2 -> Board position 27
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 26 to 27 and gets a bonus roll!
Player Red gets a bonus roll

Red rolls 5
DEBUG: Piece Red1 at pos 14, die_roll 5, dist_from_home is 34, dist_from_start is 17, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 27, die_roll 5, dist_from_home is 45, dist_from_start is 6, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L14 to L9 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 9
Piece R2 -> Board position 27
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 14 to 9


Green rolls 2
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L39 to L41 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Base
Piece G4 -> Board position 41
GREEN moves piece G4 from 39 to 41


Yellow rolls 6
DEBUG: Piece Yellow3 at pos 50, die_roll 6, dist_from_home is 43, dist_from_start is 8, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L50 to L44 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 44
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 50 to 44


Blue rolls 1
DEBUG: Piece Blue3 at pos 48, die_roll 1, dist_from_home is 15, dist_from_start is 36, home_entry at 11 direction is Clockwise
[Blue] moves piece B3 from location L48 to L49 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 49
Piece B2 -> Home
Piece B3 -> Board position 49
Piece B4 -> Board position 45
BLUE moves piece B3 from 48 to 49


Red rolls 3
DEBUG: Piece Red1 at pos 9, die_roll 3, dist_from_home is 31, dist_from_start is 20, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 27, die_roll 3, dist_from_home is 47, dist_from_start is 4, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L9 to L6 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 6
Piece R2 -> Board position 27
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 9 to 6


Green rolls 6
DEBUG: Piece Green4 at pos 41, die_roll 6, dist_from_home is 43, dist_from_start is 8, home_entry at 37 direction is Clockwise
Piece Green got direction Clockwise
[Green] player moves piece G3 to the starting point.
[Green] player now has 2/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 41
GREEN moves piece G3 from 0 to 39


Yellow rolls 4
DEBUG: Piece Yellow3 at pos 44, die_roll 4, dist_from_home is 39, dist_from_start is 12, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L44 to L40 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 40
Piece Y4 -> Home path position 0
YELLOW moves piece Y3 from 44 to 40


Blue rolls 1
DEBUG: Piece Blue4 at pos 45, die_roll 1, dist_from_home is 18, dist_from_start is 33, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L45 to L46 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 49
Piece B2 -> Home
Piece B3 -> Board position 49
Piece B4 -> Board position 46
BLUE moves piece B4 from 45 to 46


Red rolls 3
DEBUG: Piece Red1 at pos 6, die_roll 3, dist_from_home is 28, dist_from_start is 23, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 27, die_roll 3, dist_from_home is 47, dist_from_start is 4, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L6 to L3 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 3
Piece R2 -> Board position 27
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 6 to 3


Green rolls 4
DEBUG: Piece Green3 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 41, die_roll 4, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L41 to L45 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 45
GREEN moves piece G4 from 41 to 45


Yellow rolls 5
DEBUG: Piece Yellow3 at pos 40, die_roll 5, dist_from_home is 34, dist_from_start is 17, home_entry at 50 direction is Counter-Clockwise
DEBUG: Reaching final home position
DEBUG: Piece Yellow4 reaches final home
[Yellow] piece Y4 reaches home!
[Yellow] player now has 3/4 pieces at home.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 40
Piece Y4 -> Home
YELLOW moves piece Y4 from 0 to 5


Blue rolls 2
DEBUG: Piece Blue1 at pos 49, die_roll 2, dist_from_home is 33, dist_from_start is 18, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L49 to L47 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 47
Piece B2 -> Home
Piece B3 -> Board position 49
Piece B4 -> Board position 46
BLUE moves piece B1 from 49 to 47


Red rolls 2
DEBUG: Piece Red1 at pos 3, die_roll 2, dist_from_home is 26, dist_from_start is 25, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 27, die_roll 2, dist_from_home is 48, dist_from_start is 3, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L3 to L1 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 1
Piece R2 -> Board position 27
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 3 to 1


Green rolls 1
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 45, die_roll 1, dist_from_home is 44, dist_from_start is 7, home_entry at 37 direction is Clockwise
CAPTURE: [Green] piece G4 lands on square 46, captures [Blue] piece B4, and returns it to the base.
[Blue] player now has 2/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 46
GREEN moves piece G4 from 45 to 46 and gets a bonus roll!
Player Green gets a bonus roll

Green rolls 3
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 46, die_roll 3, dist_from_home is 41, dist_from_start is 10, home_entry at 37 direction is Clockwise
CAPTURE: [Green] piece G4 lands on square 49, captures [Blue] piece B3, and returns it to the base.
[Blue] player now has 1/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 49
GREEN moves piece G4 from 46 to 49 and gets a bonus roll!
Player Green gets a bonus roll

Green rolls 1
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 49, die_roll 1, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L49 to L50 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 50
GREEN moves piece G4 from 49 to 50


Yellow rolls 6
DEBUG: Piece Yellow3 at pos 40, die_roll 6, dist_from_home is 33, dist_from_start is 18, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L40 to L34 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 34
Piece Y4 -> Home
YELLOW moves piece Y3 from 40 to 34


Blue rolls 5
DEBUG: Piece Blue1 at pos 47, die_roll 5, dist_from_home is 28, dist_from_start is 23, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L47 to L42 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 42
Piece B2 -> Home
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B1 from 47 to 42


Red rolls 5
DEBUG: Piece Red1 at pos 1, die_roll 5, dist_from_home is 21, dist_from_start is 30, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 27, die_roll 5, dist_from_home is 45, dist_from_start is 6, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L1 to L48 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 48
Piece R2 -> Board position 27
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 1 to 48


Green rolls 4
DEBUG: Piece Green3 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 50, die_roll 4, dist_from_home is 36, dist_from_start is 15, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L50 to L2 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 2
GREEN moves piece G4 from 50 to 2


Yellow rolls 3
DEBUG: Piece Yellow3 at pos 34, die_roll 3, dist_from_home is 30, dist_from_start is 21, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L34 to L31 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 31
Piece Y4 -> Home
YELLOW moves piece Y3 from 34 to 31


Blue rolls 1
DEBUG: Piece Blue1 at pos 42, die_roll 1, dist_from_home is 27, dist_from_start is 24, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L42 to L41 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 41
Piece B2 -> Home
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B1 from 42 to 41


Red rolls 4
DEBUG: Piece Red1 at pos 48, die_roll 4, dist_from_home is 17, dist_from_start is 34, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 27, die_roll 4, dist_from_home is 46, dist_from_start is 5, home_entry at 24 direction is Clockwise
CAPTURE: [Red] piece R2 lands on square 31, captures [Yellow] piece Y3, and returns it to the base.
[Yellow] player now has 0/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 48
Piece R2 -> Board position 31
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 27 to 31 and gets a bonus roll!
Player Red gets a bonus roll

Red rolls 6
DEBUG: Piece Red1 at pos 48, die_roll 6, dist_from_home is 15, dist_from_start is 36, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 31, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L48 to L42 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 42
Piece R2 -> Board position 31
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 48 to 42


Green rolls 2
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 2, die_roll 2, dist_from_home is 34, dist_from_start is 17, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L2 to L4 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 4
GREEN moves piece G4 from 2 to 4


Yellow rolls 6
Piece Yellow got direction Counter-Clockwise
[Yellow] player moves piece Y3 to the starting point.
[Yellow] player now has 1/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Home
YELLOW moves piece Y3 from 0 to 0


Blue rolls 4
DEBUG: Piece Blue1 at pos 41, die_roll 4, dist_from_home is 23, dist_from_start is 28, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L41 to L37 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 37
Piece B2 -> Home
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B1 from 41 to 37


Red rolls 2
DEBUG: Piece Red1 at pos 42, die_roll 2, dist_from_home is 13, dist_from_start is 38, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 31, die_roll 2, dist_from_home is 44, dist_from_start is 7, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L42 to L40 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 40
Piece R2 -> Board position 31
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 42 to 40


Green rolls 6
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 4, die_roll 6, dist_from_home is 28, dist_from_start is 23, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L4 to L10 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 10
GREEN moves piece G4 from 4 to 10


Yellow rolls 4
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L0 to L48 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 48
Piece Y4 -> Home
YELLOW moves piece Y3 from 0 to 48


Blue rolls 3
DEBUG: Piece Blue1 at pos 37, die_roll 3, dist_from_home is 20, dist_from_start is 31, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B1 from location L37 to L34 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 34
Piece B2 -> Home
Piece B3 -> Base
Piece B4 -> Base
BLUE moves piece B1 from 37 to 34


Red rolls 6
DEBUG: Piece Red1 at pos 40, die_roll 6, dist_from_home is 7, dist_from_start is 44, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 31, die_roll 6, dist_from_home is 40, dist_from_start is 11, home_entry at 24 direction is Clockwise
CAPTURE: [Red] piece R1 lands on square 34, captures [Blue] piece B1, and returns it to the base.
[Blue] player now has 0/4 pieces on the board and 3/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 34
Piece R2 -> Board position 31
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 40 to 34 and gets a bonus roll!
Player Red gets a bonus roll

Red rolls 3
DEBUG: Piece Red1 at pos 34, die_roll 3, dist_from_home is 4, dist_from_start is 47, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 31, die_roll 3, dist_from_home is 43, dist_from_start is 8, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L34 to L31 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 31
Piece R2 -> Board position 31
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 34 to 31


Green rolls 4
DEBUG: Piece Green3 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 10, die_roll 4, dist_from_home is 24, dist_from_start is 27, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L10 to L14 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 14
GREEN moves piece G4 from 10 to 14


Yellow rolls 3
DEBUG: Piece Yellow3 at pos 48, die_roll 3, dist_from_home is 44, dist_from_start is 7, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L48 to L45 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 45
Piece Y4 -> Home
YELLOW moves piece Y3 from 48 to 45


Blue rolls 6
Piece Blue got direction Counter-Clockwise
[Blue] player moves piece B3 to the starting point.
[Blue] player now has 1/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 13
Piece B4 -> Base
BLUE moves piece B3 from 0 to 13


Red rolls 3
DEBUG: Piece Red1 at pos 31, die_roll 3, dist_from_home is 1, dist_from_start is 50, home_entry at 24 direction is Counter-Clockwise
DEBUG: Piece Red2 at pos 31, die_roll 3, dist_from_home is 43, dist_from_start is 8, home_entry at 24 direction is Clockwise
[Red] moves piece R1 from location L31 to L28 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Board position 28
Piece R2 -> Board position 31
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 31 to 28


Green rolls 3
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 14, die_roll 3, dist_from_home is 21, dist_from_start is 30, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L14 to L17 by 3 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 17
GREEN moves piece G4 from 14 to 17


Yellow rolls 1
DEBUG: Piece Yellow3 at pos 45, die_roll 1, dist_from_home is 43, dist_from_start is 8, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L45 to L44 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 44
Piece Y4 -> Home
YELLOW moves piece Y3 from 45 to 44


Blue rolls 3
DEBUG: Piece Blue3 at pos 13, die_roll 3, dist_from_home is 48, dist_from_start is 3, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L13 to L10 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 10
Piece B4 -> Base
BLUE moves piece B3 from 13 to 10


Red rolls 3
DEBUG: Piece Red1 at pos 28, die_roll 3, dist_from_home is -2, dist_from_start is 53, home_entry at 24 direction is Counter-Clockwise
DEBUG: Crosses home entry at step 1, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Piece Red2 at pos 31, die_roll 3, dist_from_home is 43, dist_from_start is 8, home_entry at 24 direction is Clockwise
DEBUG: Applying move to home path, home_index = 1
[Red] moves piece R1 from location L28 to L1 by 25 units in Counter-Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home path position 1
Piece R2 -> Board position 31
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 28 to 1


Green rolls 2
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 17, die_roll 2, dist_from_home is 19, dist_from_start is 32, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L17 to L19 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 19
GREEN moves piece G4 from 17 to 19


Yellow rolls 4
DEBUG: Piece Yellow3 at pos 44, die_roll 4, dist_from_home is 39, dist_from_start is 12, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L44 to L40 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 40
Piece Y4 -> Home
YELLOW moves piece Y3 from 44 to 40


Blue rolls 3
DEBUG: Piece Blue3 at pos 10, die_roll 3, dist_from_home is 45, dist_from_start is 6, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L10 to L7 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 7
Piece B4 -> Base
BLUE moves piece B3 from 10 to 7


Red rolls 3
DEBUG: Piece Red2 at pos 31, die_roll 3, dist_from_home is 43, dist_from_start is 8, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L31 to L34 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home path position 1
Piece R2 -> Board position 34
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 31 to 34


Green rolls 1
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 19, die_roll 1, dist_from_home is 18, dist_from_start is 33, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L19 to L20 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 20
GREEN moves piece G4 from 19 to 20


Yellow rolls 4
DEBUG: Piece Yellow3 at pos 40, die_roll 4, dist_from_home is 35, dist_from_start is 16, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L40 to L36 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 36
Piece Y4 -> Home
YELLOW moves piece Y3 from 40 to 36


Blue rolls 5
DEBUG: Piece Blue3 at pos 7, die_roll 5, dist_from_home is 40, dist_from_start is 11, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L7 to L2 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 2
Piece B4 -> Base
BLUE moves piece B3 from 7 to 2


Red rolls 4
DEBUG: Reaching final home position
DEBUG: Piece Red2 at pos 34, die_roll 4, dist_from_home is 39, dist_from_start is 12, home_entry at 24 direction is Clockwise
DEBUG: Piece Red1 reaches final home
[Red] piece R1 reaches home!
[Red] player now has 1/4 pieces at home.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 34
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R1 from 1 to 5


Green rolls 1
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 20, die_roll 1, dist_from_home is 17, dist_from_start is 34, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L20 to L21 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 21
GREEN moves piece G4 from 20 to 21


Yellow rolls 1
DEBUG: Piece Yellow3 at pos 36, die_roll 1, dist_from_home is 34, dist_from_start is 17, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L36 to L35 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 35
Piece Y4 -> Home
YELLOW moves piece Y3 from 36 to 35


Blue rolls 6
Piece Blue got direction Clockwise
[Blue] player moves piece B4 to the starting point.
[Blue] player now has 2/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 2
Piece B4 -> Board position 13
BLUE moves piece B4 from 0 to 13


Red rolls 4
DEBUG: Piece Red2 at pos 34, die_roll 4, dist_from_home is 39, dist_from_start is 12, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L34 to L38 by 4 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 38
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 34 to 38


Green rolls 5
DEBUG: Piece Green3 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 21, die_roll 5, dist_from_home is 12, dist_from_start is 39, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L21 to L26 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 26
GREEN moves piece G4 from 21 to 26


Yellow rolls 3
DEBUG: Piece Yellow3 at pos 35, die_roll 3, dist_from_home is 31, dist_from_start is 20, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L35 to L32 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 32
Piece Y4 -> Home
YELLOW moves piece Y3 from 35 to 32


Blue rolls 2
DEBUG: Piece Blue3 at pos 2, die_roll 2, dist_from_home is 38, dist_from_start is 13, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L2 to L0 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 0
Piece B4 -> Board position 13
BLUE moves piece B3 from 2 to 0


Red rolls 4
DEBUG: Piece Red2 at pos 38, die_roll 4, dist_from_home is 35, dist_from_start is 16, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L38 to L42 by 4 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 42
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 38 to 42


Green rolls 6
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 26, die_roll 6, dist_from_home is 6, dist_from_start is 45, home_entry at 37 direction is Clockwise
CAPTURE: [Green] piece G4 lands on square 32, captures [Yellow] piece Y3, and returns it to the base.
[Yellow] player now has 0/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 32
GREEN moves piece G4 from 26 to 32 and gets a bonus roll!
Player Green gets a bonus roll

Green rolls 1
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 32, die_roll 1, dist_from_home is 5, dist_from_start is 46, home_entry at 37 direction is Clockwise
[Green] moves piece G4 from location L32 to L33 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 33
GREEN moves piece G4 from 32 to 33


Yellow rolls 4
YELLOW cannot move this turn.


Blue rolls 1
DEBUG: Piece Blue4 at pos 13, die_roll 1, dist_from_home is 50, dist_from_start is 1, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L13 to L14 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 0
Piece B4 -> Board position 14
BLUE moves piece B4 from 13 to 14


Red rolls 5
DEBUG: Piece Red2 at pos 42, die_roll 5, dist_from_home is 30, dist_from_start is 21, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L42 to L47 by 5 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 47
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 42 to 47


Green rolls 5
DEBUG: Piece Green3 at pos 39, die_roll 5, dist_from_home is 46, dist_from_start is 5, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 33, die_roll 5, dist_from_home is 0, dist_from_start is 51, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 5, steps_into_home = 0
[Green] moves piece G4 from location L33 to L38 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Board position 38
GREEN moves piece G4 from 33 to 38


Yellow rolls 1
YELLOW cannot move this turn.


Blue rolls 1
DEBUG: Piece Blue3 at pos 0, die_roll 1, dist_from_home is 37, dist_from_start is 14, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L0 to L51 by 51 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 51
Piece B4 -> Board position 14
BLUE moves piece B3 from 0 to 51


Red rolls 5
DEBUG: Piece Red2 at pos 47, die_roll 5, dist_from_home is 25, dist_from_start is 26, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L47 to L0 by 5 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 0
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 47 to 0


Green rolls 4
DEBUG: Piece Green3 at pos 39, die_roll 4, dist_from_home is 47, dist_from_start is 4, home_entry at 37 direction is Clockwise
DEBUG: Piece Green4 at pos 38, die_roll 4, dist_from_home is -4, dist_from_start is 55, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 0, steps_into_home = 4
DEBUG: Entering home path at index 4
DEBUG: Applying move to home path, home_index = 3
[Green] moves piece G4 from location L38 to L3 by 17 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Home path position 3
GREEN moves piece G4 from 38 to 3


Yellow rolls 5
YELLOW cannot move this turn.


Blue rolls 4
DEBUG: Piece Blue4 at pos 14, die_roll 4, dist_from_home is 46, dist_from_start is 5, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L14 to L18 by 4 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 51
Piece B4 -> Board position 18
BLUE moves piece B4 from 14 to 18


Red rolls 3
DEBUG: Piece Red2 at pos 0, die_roll 3, dist_from_home is 22, dist_from_start is 29, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L0 to L3 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 3
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 0 to 3


Green rolls 2
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 37 direction is Clockwise
DEBUG: Reaching final home position
DEBUG: Piece Green4 reaches final home
[Green] piece G4 reaches home!
[Green] player now has 3/4 pieces at home.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 39
Piece G4 -> Home
GREEN moves piece G4 from 3 to 5


Yellow rolls 3
YELLOW cannot move this turn.


Blue rolls 3
DEBUG: Piece Blue3 at pos 51, die_roll 3, dist_from_home is 34, dist_from_start is 17, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L51 to L48 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 48
Piece B4 -> Board position 18
BLUE moves piece B3 from 51 to 48


Red rolls 2
DEBUG: Piece Red2 at pos 3, die_roll 2, dist_from_home is 20, dist_from_start is 31, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L3 to L5 by 2 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 5
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 3 to 5


Green rolls 6
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L39 to L45 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 45
Piece G4 -> Home
GREEN moves piece G3 from 39 to 45


Yellow rolls 2
YELLOW cannot move this turn.


Blue rolls 5
DEBUG: Piece Blue4 at pos 18, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L18 to L23 by 5 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 48
Piece B4 -> Board position 23
BLUE moves piece B4 from 18 to 23


Red rolls 3
DEBUG: Piece Red2 at pos 5, die_roll 3, dist_from_home is 17, dist_from_start is 34, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L5 to L8 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 8
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 5 to 8


Green rolls 4
DEBUG: Piece Green3 at pos 45, die_roll 4, dist_from_home is 41, dist_from_start is 10, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L45 to L49 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 49
Piece G4 -> Home
GREEN moves piece G3 from 45 to 49


Yellow rolls 5
YELLOW cannot move this turn.


Blue rolls 6
Piece Blue got direction Clockwise
[Blue] player moves piece B1 to the starting point.
[Blue] player now has 3/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Home
Piece B3 -> Board position 48
Piece B4 -> Board position 23
BLUE moves piece B1 from 0 to 13


Red rolls 1
DEBUG: Piece Red2 at pos 8, die_roll 1, dist_from_home is 16, dist_from_start is 35, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L8 to L9 by 1 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 9
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 8 to 9


Green rolls 1
DEBUG: Piece Green3 at pos 49, die_roll 1, dist_from_home is 40, dist_from_start is 11, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L49 to L50 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 50
Piece G4 -> Home
GREEN moves piece G3 from 49 to 50


Yellow rolls 3
YELLOW cannot move this turn.


Blue rolls 6
DEBUG: Piece Blue3 at pos 48, die_roll 6, dist_from_home is 28, dist_from_start is 23, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L48 to L42 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Home
Piece B3 -> Board position 42
Piece B4 -> Board position 23
BLUE moves piece B3 from 48 to 42


Red rolls 4
DEBUG: Piece Red2 at pos 9, die_roll 4, dist_from_home is 12, dist_from_start is 39, home_entry at 24 direction is Clockwise
CAPTURE: [Red] piece R2 lands on square 13, captures [Blue] piece B1, and returns it to the base.
[Blue] player now has 2/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 13
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 9 to 13 and gets a bonus roll!
Player Red gets a bonus roll

Red rolls 6
DEBUG: Piece Red2 at pos 13, die_roll 6, dist_from_home is 6, dist_from_start is 45, home_entry at 24 direction is Clockwise
Piece Red got direction Counter-Clockwise
[Red] player moves piece R3 to the starting point.
[Red] player now has 2/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 13
Piece R3 -> Board position 26
Piece R4 -> Base
RED moves piece R3 from 0 to 26


Green rolls 6
DEBUG: Piece Green3 at pos 50, die_roll 6, dist_from_home is 34, dist_from_start is 17, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L50 to L4 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 4
Piece G4 -> Home
GREEN moves piece G3 from 50 to 4


Yellow rolls 1
YELLOW cannot move this turn.


Blue rolls 3
DEBUG: Piece Blue4 at pos 23, die_roll 3, dist_from_home is 38, dist_from_start is 13, home_entry at 11 direction is Clockwise
CAPTURE: [Blue] piece B4 lands on square 26, captures [Red] piece R3, and returns it to the base.
[Red] player now has 1/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 42
Piece B4 -> Board position 26
BLUE moves piece B4 from 23 to 26 and gets a bonus roll!
Player Blue gets a bonus roll

Blue rolls 2
DEBUG: Piece Blue3 at pos 42, die_roll 2, dist_from_home is 26, dist_from_start is 25, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L42 to L40 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 40
Piece B4 -> Board position 26
BLUE moves piece B3 from 42 to 40


Red rolls 1
DEBUG: Piece Red2 at pos 13, die_roll 1, dist_from_home is 11, dist_from_start is 40, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L13 to L14 by 1 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 14
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 13 to 14


Green rolls 2
DEBUG: Piece Green3 at pos 4, die_roll 2, dist_from_home is 32, dist_from_start is 19, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L4 to L6 by 2 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 6
Piece G4 -> Home
GREEN moves piece G3 from 4 to 6


Yellow rolls 5
YELLOW cannot move this turn.


Blue rolls 4
DEBUG: Piece Blue4 at pos 26, die_roll 4, dist_from_home is 34, dist_from_start is 17, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L26 to L30 by 4 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 40
Piece B4 -> Board position 30
BLUE moves piece B4 from 26 to 30


Red rolls 5
DEBUG: Piece Red2 at pos 14, die_roll 5, dist_from_home is 6, dist_from_start is 45, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L14 to L19 by 5 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 19
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 14 to 19


Green rolls 6
DEBUG: Piece Green3 at pos 6, die_roll 6, dist_from_home is 26, dist_from_start is 25, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L6 to L12 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 12
Piece G4 -> Home
GREEN moves piece G3 from 6 to 12


Yellow rolls 6
Piece Yellow got direction Counter-Clockwise
[Yellow] player moves piece Y3 to the starting point.
[Yellow] player now has 1/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 0
Piece Y4 -> Home
YELLOW moves piece Y3 from 0 to 0


Blue rolls 2
DEBUG: Piece Blue3 at pos 40, die_roll 2, dist_from_home is 24, dist_from_start is 27, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L40 to L38 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 38
Piece B4 -> Board position 30
BLUE moves piece B3 from 40 to 38


Red rolls 2
DEBUG: Piece Red2 at pos 19, die_roll 2, dist_from_home is 4, dist_from_start is 47, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L19 to L21 by 2 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 21
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 19 to 21


Green rolls 4
DEBUG: Piece Green3 at pos 12, die_roll 4, dist_from_home is 22, dist_from_start is 29, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L12 to L16 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 16
Piece G4 -> Home
GREEN moves piece G3 from 12 to 16


Yellow rolls 2
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 49, dist_from_start is 2, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L0 to L50 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 50
Piece Y4 -> Home
YELLOW moves piece Y3 from 0 to 50


Blue rolls 6
DEBUG: Piece Blue4 at pos 30, die_roll 6, dist_from_home is 28, dist_from_start is 23, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L30 to L36 by 6 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Base
Piece B2 -> Home
Piece B3 -> Board position 38
Piece B4 -> Board position 36
BLUE moves piece B4 from 30 to 36


Red rolls 3
DEBUG: Piece Red2 at pos 21, die_roll 3, dist_from_home is 1, dist_from_start is 50, home_entry at 24 direction is Clockwise
[Red] moves piece R2 from location L21 to L24 by 3 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Board position 24
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 21 to 24


Green rolls 6
DEBUG: Piece Green3 at pos 16, die_roll 6, dist_from_home is 16, dist_from_start is 35, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L16 to L22 by 6 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 22
Piece G4 -> Home
GREEN moves piece G3 from 16 to 22


Yellow rolls 3
DEBUG: Piece Yellow3 at pos 50, die_roll 3, dist_from_home is 46, dist_from_start is 5, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L50 to L47 by 49 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 47
Piece Y4 -> Home
YELLOW moves piece Y3 from 50 to 47


Blue rolls 6
Piece Blue got direction Clockwise
[Blue] player moves piece B1 to the starting point.
[Blue] player now has 3/4 pieces on the board and 0/4 pieces on the base.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Home
Piece B3 -> Board position 38
Piece B4 -> Board position 36
BLUE moves piece B1 from 0 to 13


Red rolls 5
DEBUG: Piece Red2 at pos 24, die_roll 5, dist_from_home is -4, dist_from_start is 55, home_entry at 24 direction is Clockwise
DEBUG: Crosses home entry at step 1, steps_into_home = 4
DEBUG: Entering home path at index 4
DEBUG: Applying move to home path, home_index = 3
[Red] moves piece R2 from location L24 to L3 by 31 units in Clockwise direction.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Home path position 3
Piece R3 -> Base
Piece R4 -> Base
RED moves piece R2 from 24 to 3


Green rolls 1
DEBUG: Piece Green3 at pos 22, die_roll 1, dist_from_home is 15, dist_from_start is 36, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L22 to L23 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 23
Piece G4 -> Home
GREEN moves piece G3 from 22 to 23


Yellow rolls 5
DEBUG: Piece Yellow3 at pos 47, die_roll 5, dist_from_home is 41, dist_from_start is 10, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L47 to L42 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 42
Piece Y4 -> Home
YELLOW moves piece Y3 from 47 to 42


Blue rolls 4
DEBUG: Piece Blue3 at pos 38, die_roll 4, dist_from_home is 20, dist_from_start is 31, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L38 to L34 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Home
Piece B3 -> Board position 34
Piece B4 -> Board position 36
BLUE moves piece B3 from 38 to 34


Red rolls 3
RED cannot move this turn.


Green rolls 4
DEBUG: Piece Green3 at pos 23, die_roll 4, dist_from_home is 11, dist_from_start is 40, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L23 to L27 by 4 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 27
Piece G4 -> Home
GREEN moves piece G3 from 23 to 27


Yellow rolls 5
DEBUG: Piece Yellow3 at pos 42, die_roll 5, dist_from_home is 36, dist_from_start is 15, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L42 to L37 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 37
Piece Y4 -> Home
YELLOW moves piece Y3 from 42 to 37


Blue rolls 6
DEBUG: Piece Blue4 at pos 36, die_roll 6, dist_from_home is 22, dist_from_start is 29, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L36 to L42 by 6 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 13
Piece B2 -> Home
Piece B3 -> Board position 34
Piece B4 -> Board position 42
BLUE moves piece B4 from 36 to 42


Red rolls 3
RED cannot move this turn.


Green rolls 5
DEBUG: Piece Green3 at pos 27, die_roll 5, dist_from_home is 6, dist_from_start is 45, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L27 to L32 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 32
Piece G4 -> Home
GREEN moves piece G3 from 27 to 32


Yellow rolls 6
DEBUG: Piece Yellow3 at pos 37, die_roll 6, dist_from_home is 30, dist_from_start is 21, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L37 to L31 by 46 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 31
Piece Y4 -> Home
YELLOW moves piece Y3 from 37 to 31


Blue rolls 6
DEBUG: Piece Blue1 at pos 13, die_roll 6, dist_from_home is 45, dist_from_start is 6, home_entry at 11 direction is Clockwise
[Blue] moves piece B1 from location L13 to L19 by 6 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 19
Piece B2 -> Home
Piece B3 -> Board position 34
Piece B4 -> Board position 42
BLUE moves piece B1 from 13 to 19


Red rolls 6
Piece Red got direction Clockwise
[Red] player moves piece R3 to the starting point.
[Red] player now has 1/4 pieces on the board and 1/4 pieces on the base.

============================
Location of pieces [Red]
============================
Piece R1 -> Home
Piece R2 -> Home path position 3
Piece R3 -> Board position 26
Piece R4 -> Base
RED moves piece R3 from 0 to 26


Green rolls 5
DEBUG: Piece Green3 at pos 32, die_roll 5, dist_from_home is 1, dist_from_start is 50, home_entry at 37 direction is Clockwise
[Green] moves piece G3 from location L32 to L37 by 5 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 37
Piece G4 -> Home
GREEN moves piece G3 from 32 to 37


Yellow rolls 5
DEBUG: Piece Yellow3 at pos 31, die_roll 5, dist_from_home is 25, dist_from_start is 26, home_entry at 50 direction is Counter-Clockwise
CAPTURE: [Yellow] piece Y3 lands on square 26, captures [Red] piece R3, and returns it to the base.
[Red] player now has 0/4 pieces on the board and 2/4 pieces on the base.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 26
Piece Y4 -> Home
YELLOW moves piece Y3 from 31 to 26 and gets a bonus roll!
Player Yellow gets a bonus roll

Yellow rolls 4
DEBUG: Piece Yellow3 at pos 26, die_roll 4, dist_from_home is 21, dist_from_start is 30, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L26 to L22 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 22
Piece Y4 -> Home
YELLOW moves piece Y3 from 26 to 22


Blue rolls 4
DEBUG: Piece Blue3 at pos 34, die_roll 4, dist_from_home is 16, dist_from_start is 35, home_entry at 11 direction is Counter-Clockwise
[Blue] moves piece B3 from location L34 to L30 by 48 units in Counter-Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 19
Piece B2 -> Home
Piece B3 -> Board position 30
Piece B4 -> Board position 42
BLUE moves piece B3 from 34 to 30


Red rolls 3
RED cannot move this turn.


Green rolls 1
DEBUG: Piece Green3 at pos 37, die_roll 1, dist_from_home is 0, dist_from_start is 51, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 1, steps_into_home = 0
[Green] moves piece G3 from location L37 to L38 by 1 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Board position 38
Piece G4 -> Home
GREEN moves piece G3 from 37 to 38


Yellow rolls 2
DEBUG: Piece Yellow3 at pos 22, die_roll 2, dist_from_home is 19, dist_from_start is 32, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L22 to L20 by 50 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 20
Piece Y4 -> Home
YELLOW moves piece Y3 from 22 to 20


Blue rolls 1
DEBUG: Piece Blue4 at pos 42, die_roll 1, dist_from_home is 21, dist_from_start is 30, home_entry at 11 direction is Clockwise
[Blue] moves piece B4 from location L42 to L43 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 19
Piece B2 -> Home
Piece B3 -> Board position 30
Piece B4 -> Board position 43
BLUE moves piece B4 from 42 to 43


Red rolls 3
RED cannot move this turn.


Green rolls 2
DEBUG: Piece Green3 at pos 38, die_roll 2, dist_from_home is -2, dist_from_start is 53, home_entry at 37 direction is Clockwise
DEBUG: Crosses home entry at step 0, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Applying move to home path, home_index = 1
[Green] moves piece G3 from location L38 to L1 by 15 units in Clockwise direction.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Home path position 1
Piece G4 -> Home
GREEN moves piece G3 from 38 to 1


Yellow rolls 5
DEBUG: Piece Yellow3 at pos 20, die_roll 5, dist_from_home is 14, dist_from_start is 37, home_entry at 50 direction is Counter-Clockwise
[Yellow] moves piece Y3 from location L20 to L15 by 47 units in Counter-Clockwise direction.

============================
Location of pieces [Yellow]
============================
Piece Y1 -> Home
Piece Y2 -> Home
Piece Y3 -> Board position 15
Piece Y4 -> Home
YELLOW moves piece Y3 from 20 to 15


Blue rolls 1
DEBUG: Piece Blue1 at pos 19, die_roll 1, dist_from_home is 44, dist_from_start is 7, home_entry at 11 direction is Clockwise
[Blue] moves piece B1 from location L19 to L20 by 1 units in Clockwise direction.

============================
Location of pieces [Blue]
============================
Piece B1 -> Board position 20
Piece B2 -> Home
Piece B3 -> Board position 30
Piece B4 -> Board position 43
BLUE moves piece B1 from 19 to 20


Red rolls 3
RED cannot move this turn.


Green rolls 4
DEBUG: Reaching final home position
DEBUG: Piece Green3 reaches final home
[Green] piece G3 reaches home!
[Green] player now has 4/4 pieces at home.

============================
Location of pieces [Green]
============================
Piece G1 -> Home
Piece G2 -> Home
Piece G3 -> Home
Piece G4 -> Home
GREEN moves piece G3 from 1 to 5



Player Green wins!!!
