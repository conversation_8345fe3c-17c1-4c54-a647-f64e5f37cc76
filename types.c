#include "types.h"
#include <stdio.h>

// start positions
const int START_POSITION[NUM_PLAYERS] = {
  0, // yellow
  13, // blue
  26, // red
  39 // green
};
const int HOME_ENTRY_POSITION[NUM_PLAYERS] = {
  50, //yellow
  11, //blue
  24, // red,
  37 //green
};
// define actual pieces
Piece all_pieces[NUM_PLAYERS][PIECES_PER_PLAYER];

// define main board
Cell board[STD_BOARD_SIZE];
Player players[NUM_PLAYERS];

// helper function for color name
const char* get_color_name(Color c) {
  switch (c) {
    case RED: return "Red";
    case GREEN: return "Green";
    case YELLOW: return "Yellow";
    case BLUE: return "Blue";
    default: return "Unknown";
  }
}

const char* get_direction(Direction d) {
  switch(d) {
    case CLOCKWISE: return "Clockwise";
    case ANTICLOCKWISE: return "Counter-Clockwise";
    default: return "Unknown";
  }
}


void initialize_pieces() {
  for (int c = 0; c < NUM_PLAYERS; c++) {
    for (int p = 0; p < PIECES_PER_PLAYER; p++) {
      all_pieces[c][p].color = c;
      all_pieces[c][p].id = p+1;  // 1 based id 1-4
      all_pieces[c][p].is_in_base = 1;
      all_pieces[c][p].is_in_board = 0;
      all_pieces[c][p].is_in_home_path = 0;
      all_pieces[c][p].is_in_home = 0;
      all_pieces[c][p].home_index = -1; //invalid for now
      all_pieces[c][p].board_pos = -1;
      all_pieces[c][p].direction = CLOCKWISE;
      all_pieces[c][p].original_direction = UNKNOWN; // CS-5: Initialize original direction

      all_pieces[c][p].remaining_to_home = STD_BOARD_SIZE-1;
      all_pieces[c][p].dist_from_start = 0;

      all_pieces[c][p].captures = 0;

    }
  }
}

void initialize_board() {
  for (int i = 0; i < STD_BOARD_SIZE; i++ ){
    board[i].count = 0;
    board[i].superblock = 0;
    board[i].block_direction = UNKNOWN;
    for (int j = 0; j < PIECES_PER_PLAYER; j++){
      board[i].occupants[j] = NULL;
    }
  }
}

void initialize_players() {
  for (int i = 0; i < NUM_PLAYERS; i++) {
      players[i].color = i;
      players[i].id = i;
      players[i].order = i;
      players[i].in_base = PIECES_PER_PLAYER;
      players[i].in_board = 0;
      players[i].in_home = 0;
      // players[i].last_move = &all_pieces[i][PIECES_PER_PLAYER-1]; // Assuming the last move is initialized to the last piece of the player
      players[i].last_move = NULL; // Assuming the last move is initialized to the last piece of the player
      players[i].last_victim = NULL; // Assuming the last move is initialized to the last piece of the player
      for (int j=0; j <PIECES_PER_PLAYER;j++){
        players[i].blocks[j] = NULL;
      }
    }
}
